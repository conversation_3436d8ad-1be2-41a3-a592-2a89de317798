# SEO Agent with DataForSEO MCP Integration

This is an advanced SEO agent built with the smolagents library, enhanced with DataForSEO MCP (Model Context Protocol) server integration for comprehensive SEO data access.

## 🚀 Features

### Core Agent Capabilities
- Advanced SEO analysis and recommendations
- Keyword research and clustering
- Competitor analysis
- SERP analysis and monitoring
- Content gap identification
- Backlink analysis

### DataForSEO MCP Integration
- **Real-time SERP Analysis**: Get live search engine results data
- **Advanced Keyword Research**: Access comprehensive keyword metrics
- **Competitor Intelligence**: Analyze competitor SEO strategies
- **Backlink Analysis**: Monitor and analyze backlink profiles
- **Domain Analytics**: Get detailed domain performance metrics
- **On-page SEO Analysis**: Crawl and analyze website performance

## 📦 Installation

### Quick Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd seo_agent
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up DataForSEO MCP server**
   ```bash
   python setup_dataforseo_mcp.py
   ```

4. **Configure your credentials**
   ```bash
   export DATAFORSEO_USERNAME='your_username'
   export DATAFORSEO_PASSWORD='your_password'
   ```

5. **Test the integration**
   ```bash
   python test_mcp_integration.py
   ```

### Manual Setup

If the automated setup doesn't work, follow the [detailed integration guide](DATAFORSEO_MCP_INTEGRATION_GUIDE.md).

## 🔧 Configuration

### Environment Variables

```bash
# Required DataForSEO credentials
DATAFORSEO_USERNAME=your_username
DATAFORSEO_PASSWORD=your_password

# Optional MCP configuration
ENABLED_MODULES="SERP,KEYWORDS_DATA,ONPAGE,DATAFORSEO_LABS,BUSINESS_DATA,DOMAIN_ANALYTICS,BACKLINKS"
DATAFORSEO_FULL_RESPONSE="false"
```

### Prerequisites

- **Node.js** (v14 or higher) - for DataForSEO MCP server
- **Python** (3.10 or higher)
- **DataForSEO API account** - [Sign up here](https://app.dataforseo.com/register)

## 🎯 Usage Examples

### Basic SEO Analysis

```python
from app import agent

# Comprehensive keyword research
result = agent.run("""
Perform keyword research for 'digital marketing tools' using MCP tools:
1. Find related keywords with search volume and difficulty
2. Analyze SERP features for top keywords
3. Provide content recommendations
""")
```

### Competitor Analysis

```python
# Analyze competitor SEO performance
result = agent.run("""
Analyze the SEO performance of semrush.com using MCP tools:
1. Get their top-ranking keywords
2. Analyze their backlink profile
3. Identify content gaps and opportunities
""")
```

### Multi-Domain Comparison

```python
# Compare multiple competitors
result = agent.run("""
Compare SEO performance of ahrefs.com, semrush.com, and moz.com:
1. Analyze keyword rankings for each
2. Compare backlink profiles
3. Identify competitive advantages
4. Provide strategic recommendations
""")
```

## 🛠️ Available Tools

### Standard Tools
- `final_answer`: Provide final responses
- `web_search`: Google search functionality
- `visit_web_page`: Web page content analysis
- `keyword_research_tool`: Basic keyword research
- `get_current_time_in_timezone`: Time utilities

### MCP-Enhanced Tools
- `mcp_serp_analysis`: Real-time SERP data analysis
- `mcp_keyword_research`: Advanced keyword research with metrics
- `mcp_competitor_analysis`: Comprehensive competitor analysis

## 📊 Example Workflows

### 1. Complete SEO Audit

```python
agent.run("""
Perform a complete SEO audit for hassconsult.com:
1. Use MCP competitor analysis to get current metrics
2. Use MCP keyword research for industry-relevant keywords
3. Use MCP SERP analysis for top target keywords
4. Identify technical SEO issues
5. Provide prioritized recommendations
""")
```

### 2. Content Strategy Development

```python
agent.run("""
Develop a content strategy for a SaaS company:
1. Research keywords for 'project management software'
2. Analyze competitor content strategies
3. Identify content gaps and opportunities
4. Create a 3-month content calendar
5. Prioritize by potential ROI
""")
```

### 3. Local SEO Analysis

```python
agent.run("""
Perform local SEO analysis for a dental practice:
1. Research local dental keywords
2. Analyze local pack opportunities
3. Compare with local competitors
4. Provide local SEO recommendations
""")
```

## 🧪 Testing

Run the test suite to verify your setup:

```bash
# Test MCP integration
python test_mcp_integration.py

# Run example analyses
python examples/mcp_seo_analysis_example.py
```

## 📚 Documentation

- [DataForSEO MCP Integration Guide](DATAFORSEO_MCP_INTEGRATION_GUIDE.md)
- [DataForSEO API Documentation](https://docs.dataforseo.com/v3/)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## 🔍 Troubleshooting

### Common Issues

1. **Node.js not found**
   - Install Node.js from [nodejs.org](https://nodejs.org)

2. **MCP server fails to start**
   - Check DataForSEO credentials
   - Verify environment variables are set

3. **Import errors**
   - Run `pip install -r requirements.txt`
   - Ensure correct Python environment

4. **Authentication errors**
   - Verify credentials at [DataForSEO dashboard](https://app.dataforseo.com)

See the [troubleshooting section](DATAFORSEO_MCP_INTEGRATION_GUIDE.md#troubleshooting) for detailed solutions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [DataForSEO](https://dataforseo.com) for providing comprehensive SEO APIs
- [smolagents](https://github.com/huggingface/smolagents) for the agent framework
- [Model Context Protocol](https://modelcontextprotocol.io/) for standardized AI tool integration

## 🚀 Quick Start Commands

```bash
# Complete setup in one go
git clone <repository-url>
cd seo_agent
pip install -r requirements.txt
python setup_dataforseo_mcp.py

# Set your credentials
export DATAFORSEO_USERNAME='your_username'
export DATAFORSEO_PASSWORD='your_password'

# Test everything
python test_mcp_integration.py

# Run example analysis
python examples/mcp_seo_analysis_example.py

# Start the agent
python app.py
```

## 📈 Advanced Features

### Batch Analysis
Process multiple domains or keywords in batch for efficiency.

### Custom Reporting
Generate detailed SEO reports with actionable insights.

### Integration Ready
Easy integration with existing SEO workflows and tools.

### Scalable Architecture
Built to handle enterprise-level SEO analysis requirements.
