[tool]
name = "prompt-builder"
version = "0.1.0"
description = "A prompt builder that manages system prompts and simulates assistant interactions."
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.dependencies]
python = "^3.8"
smolagents = "^0.1.0"  # Specify the version of the @smolagents library

[tool.poetry.dev-dependencies]
pytest = "^6.0"
pytest-cov = "^2.10"