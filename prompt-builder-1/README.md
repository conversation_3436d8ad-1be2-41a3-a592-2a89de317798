# README for Prompt Builder Project

## Overview

The Prompt Builder project is designed to manage and update system prompts for AI assistants using the @smolagents library from Hugging Face. This project allows users to easily modify different parts of the prompt, inject task examples, and simulate interactions between users and assistants.

## Features

- **Prompt Management**: Easily get, update, and inject task examples into the system prompt.
- **Prompt Updating**: Update various components of the prompt, including initial prompts, tasks, and managed agent prompts.
- **Example Management**: Store, retrieve, and validate task examples to ensure they meet required formats.
- **Simulation**: Simulate user and assistant interactions to test prompt behavior and responses.

## Installation

To install the required dependencies, run:

```bash
pip install -r requirements.txt
```

Make sure to have Python 3.7 or higher installed.

## Usage

1. **Managing Prompts**: Use the `PromptManager` class to manage your system prompts. You can retrieve the current prompt, update it, and inject task examples as needed.

2. **Updating Prompts**: The `PromptUpdater` class allows you to update specific parts of the prompt, ensuring that your assistant is always equipped with the latest information.

3. **Storing Examples**: Utilize the `ExampleStore` class to manage your task examples. Add new examples, retrieve existing ones, and validate them to ensure they are formatted correctly.

4. **Simulating Interactions**: The `AssistantSimulator` and `UserSimulator` classes can be used to simulate interactions between users and the assistant, allowing you to test how well your prompts perform in real scenarios.

## Running Tests

To run the tests for this project, use:

```bash
pytest
```

This will execute all unit tests defined in the `tests` directory.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any improvements or bug fixes.