class UserSimulator:
    def __init__(self, assistant):
        self.assistant = assistant

    def send_input(self, user_input):
        response = self.assistant.process_input(user_input)
        return response

    def simulate_interaction(self, inputs):
        responses = []
        for input_text in inputs:
            response = self.send_input(input_text)
            responses.append(response)
        return responses