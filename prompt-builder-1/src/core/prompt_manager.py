prompt-builder
├── src
│   ├── __init__.py
│   ├── core
│   │   ├── __init__.py
│   │   ├── prompt_manager.py
│   │   ├── prompt_updater.py
│   │   └── types.py
│   ├── examples
│   │   ├── __init__.py
│   │   ├── example_store.py
│   │   └── example_validator.py
│   ├── simulator
│   │   ├── __init__.py
│   │   ├── assistant.py
│   │   └── user.py
│   └── utils
│       ├── __init__.py
│       ├── logger.py
│       └── validators.py
├── tests
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_prompt_manager.py
│   ├── test_prompt_updater.py
│   └── test_simulator.py
├── .gitignore
├── LICENSE
├── README.md
├── pyproject.toml
└── requirements.txt