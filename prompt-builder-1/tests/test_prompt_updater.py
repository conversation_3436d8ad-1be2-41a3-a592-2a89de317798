# filepath: /prompt-builder/prompt-builder/tests/test_prompt_updater.py
import pytest
from src.core.prompt_updater import PromptUpdater

@pytest.fixture
def prompt_updater():
    return PromptUpdater()

def test_update_initial_prompt(prompt_updater):
    new_prompt = "This is the updated initial prompt."
    prompt_updater.update_initial_prompt(new_prompt)
    assert prompt_updater.get_initial_prompt() == new_prompt

def test_update_task_prompt(prompt_updater):
    new_task_prompt = "This is the updated task prompt."
    prompt_updater.update_task_prompt(new_task_prompt)
    assert prompt_updater.get_task_prompt() == new_task_prompt

def test_update_planning_prompt(prompt_updater):
    new_planning_prompt = "This is the updated planning prompt."
    prompt_updater.update_planning_prompt(new_planning_prompt)
    assert prompt_updater.get_planning_prompt() == new_planning_prompt

def test_update_managed_agent_prompt(prompt_updater):
    new_managed_agent_prompt = "This is the updated managed agent prompt."
    prompt_updater.update_managed_agent_prompt(new_managed_agent_prompt)
    assert prompt_updater.get_managed_agent_prompt() == new_managed_agent_prompt

def test_inject_task_example(prompt_updater):
    example = "Example task: Calculate the sum of two numbers."
    prompt_updater.inject_task_example(example)
    assert example in prompt_updater.get_task_examples()