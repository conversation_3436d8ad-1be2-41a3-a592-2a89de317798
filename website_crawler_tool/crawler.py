import asyncio
from urllib.parse import urljoin, urlparse
from utils.robots_checker import is_allowed
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

MAX_CONCURRENCY = 3  # Limit simultaneous requests

async def extract_links(page_content, base_url):
    soup = BeautifulSoup(page_content, "html.parser")
    links = set()
    for a in soup.find_all("a", href=True):
        href = urljoin(base_url, a['href'])
        if urlparse(href).netloc == urlparse(base_url).netloc:
            links.add(href.split("#")[0])  # Remove fragment
    return links

async def crawl(start_url, user_agent=None):
    visited = set()
    to_visit = set([start_url])
    semaphore = asyncio.Semaphore(MAX_CONCURRENCY)

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent=user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        )

        async def fetch(url):
            async with semaphore:
                if url in visited:
                    return set()
                if not is_allowed(url, user_agent or "Mozilla/5.0"):
                    print(f"Blocked by robots.txt: {url}")
                    return set()
                try:
                    page = await context.new_page()
                    await page.goto(url, timeout=15000)
                    content = await page.content()
                    await page.close()
                    print(f"Crawled: {url}")
                    return await extract_links(content, url)
                except Exception as e:
                    print(f"Failed: {url} ({e})")
                    return set()

        while to_visit:
            tasks = [fetch(url) for url in list(to_visit)[:MAX_CONCURRENCY]]
            results = await asyncio.gather(*tasks)
            visited.update(to_visit)
            new_links = set()
            for links in results:
                new_links.update(links)
            to_visit = new_links - visited

        await browser.close()
    print(f"\nDiscovered {len(visited)} URLs:")
    for url in visited:
        print(url)

if __name__ == "__main__":
    import sys
    from pathlib import Path
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        print("Please install beautifulsoup4: pip install beautifulsoup4")
        sys.exit(1)
    url = sys.argv[1]
    asyncio.run(crawl(url))