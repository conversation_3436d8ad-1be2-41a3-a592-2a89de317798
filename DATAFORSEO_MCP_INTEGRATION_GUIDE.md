# DataForSEO MCP Server Integration Guide

This guide explains how to integrate your SEO agent with the DataForSEO MCP (Model Context Protocol) server for enhanced SEO data access.

## Overview

The DataForSEO MCP server provides a standardized interface to access DataForSEO's comprehensive SEO APIs through the Model Context Protocol. This integration allows your agent to:

- Perform real-time SERP analysis
- Conduct advanced keyword research
- Analyze competitor data
- Access backlink information
- Get domain analytics
- Perform on-page SEO analysis

## Prerequisites

1. **Node.js** (v14 or higher)
2. **DataForSEO API credentials** (username and password)
3. **Python environment** with required packages

## Installation Steps

### 1. Run the Setup Script

```bash
python setup_dataforseo_mcp.py
```

This script will:
- Check if Node.js is installed
- Install the DataForSEO MCP server
- Help you set up environment variables
- Create MCP configuration for Claude Desktop
- Test the server installation

### 2. Manual Installation (Alternative)

If the setup script doesn't work, you can install manually:

```bash
# Install Node.js (if not already installed)
# macOS: brew install node
# Ubuntu: sudo apt-get install nodejs npm
# Windows: Download from https://nodejs.org

# Install DataForSEO MCP server
npm install -g dataforseo-mcp-server

# Or use npx (no global installation needed)
npx dataforseo-mcp-server --help
```

### 3. Set Environment Variables

Add these to your environment:

```bash
# For bash/zsh (add to ~/.bashrc or ~/.zshrc)
export DATAFORSEO_USERNAME='your_username'
export DATAFORSEO_PASSWORD='your_password'

# For Windows Command Prompt
set DATAFORSEO_USERNAME=your_username
set DATAFORSEO_PASSWORD=your_password

# For Windows PowerShell
$env:DATAFORSEO_USERNAME='your_username'
$env:DATAFORSEO_PASSWORD='your_password'
```

### 4. Install Python Dependencies

```bash
pip install -r requirements.txt
```

## Available MCP Tools

Your agent now has access to these new tools:

### 1. `mcp_serp_analysis`
Performs SERP analysis for specific keywords.

**Parameters:**
- `keyword` (str): The keyword to analyze
- `location` (str): Geographic location (default: "United States")

**Example:**
```python
result = mcp_serp_analysis("seo tools", "United States")
```

### 2. `mcp_keyword_research`
Conducts comprehensive keyword research.

**Parameters:**
- `keywords` (List[str]): List of seed keywords
- `location` (str): Geographic location (default: "United States")

**Example:**
```python
result = mcp_keyword_research(["seo", "keyword research"], "United States")
```

### 3. `mcp_competitor_analysis`
Analyzes competitor domains for SEO insights.

**Parameters:**
- `domain` (str): The domain to analyze

**Example:**
```python
result = mcp_competitor_analysis("example.com")
```

## Usage Examples

### Basic SERP Analysis

```python
# Analyze SERP for a specific keyword
serp_data = agent.run("Analyze the SERP for 'digital marketing tools' in the United States using the MCP SERP analysis tool")
```

### Comprehensive Keyword Research

```python
# Perform keyword research for multiple seed keywords
keyword_data = agent.run("Conduct keyword research for 'seo audit', 'website optimization', and 'search engine ranking' using the MCP keyword research tool")
```

### Competitor Analysis

```python
# Analyze a competitor's SEO performance
competitor_data = agent.run("Analyze the SEO performance of semrush.com using the MCP competitor analysis tool")
```

### Combined SEO Analysis

```python
# Comprehensive SEO analysis combining multiple tools
seo_analysis = agent.run("""
Perform a comprehensive SEO analysis for the website 'hassconsult.com':
1. Use MCP competitor analysis to get their current SEO metrics
2. Use MCP keyword research to find relevant keywords for their industry
3. Use MCP SERP analysis for their top 3 target keywords
4. Provide recommendations based on the data
""")
```

## Configuration

### Claude Desktop Integration

If you're using Claude Desktop, the setup script creates a configuration file at:

- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/claude/claude_desktop_config.json`

### Environment Configuration

You can customize the MCP server behavior with these environment variables:

```bash
# Required
DATAFORSEO_USERNAME=your_username
DATAFORSEO_PASSWORD=your_password

# Optional
ENABLED_MODULES="SERP,KEYWORDS_DATA,ONPAGE,DATAFORSEO_LABS,BUSINESS_DATA,DOMAIN_ANALYTICS,BACKLINKS"
DATAFORSEO_FULL_RESPONSE="false"  # Set to "true" for complete API responses
```

## Troubleshooting

### Common Issues

1. **Node.js not found**
   - Install Node.js from https://nodejs.org
   - Ensure it's in your PATH

2. **MCP server fails to start**
   - Check your DataForSEO credentials
   - Ensure environment variables are set correctly
   - Try running: `npx dataforseo-mcp-server --help`

3. **Import errors in Python**
   - Install missing dependencies: `pip install -r requirements.txt`
   - Ensure you're using the correct Python environment

4. **Authentication errors**
   - Verify your DataForSEO credentials at https://app.dataforseo.com
   - Check that environment variables are properly set

### Testing the Integration

Run this test to verify everything is working:

```python
from tools.dataforseo_mcp_client import mcp_serp_analysis

# Test the MCP integration
result = mcp_serp_analysis("test keyword", "United States")
print(result)
```

## Advanced Usage

### Custom MCP Tool Development

You can extend the MCP integration by adding new tools to `tools/dataforseo_mcp_client.py`. Follow the existing patterns:

```python
@tool
def mcp_custom_analysis(parameter: str) -> Dict[str, Any]:
    """
    Custom analysis using DataForSEO MCP server
    
    Args:
        parameter: Description of the parameter
        
    Returns:
        Dict containing analysis results
    """
    try:
        client = get_mcp_client()
        # Implementation here
        return result
    except Exception as e:
        return {"error": f"Analysis failed: {str(e)}"}
```

### Batch Processing

For large-scale analysis, consider implementing batch processing:

```python
# Example: Analyze multiple domains
domains = ["example1.com", "example2.com", "example3.com"]
results = []

for domain in domains:
    result = mcp_competitor_analysis(domain)
    results.append(result)
```

## Resources

- [DataForSEO MCP Server Documentation](https://github.com/dataforseo/mcp-server-typescript)
- [DataForSEO API Documentation](https://docs.dataforseo.com/v3/)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [DataForSEO Help Center](https://dataforseo.com/help-center/setting-up-the-official-dataforseo-mcp-server-simple-guide)

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the DataForSEO MCP server logs
3. Verify your API credentials and quotas
4. Consult the DataForSEO documentation

## Next Steps

After successful integration:

1. Test all MCP tools with your agent
2. Customize the tools for your specific use cases
3. Integrate with your existing SEO workflows
4. Consider adding more DataForSEO API endpoints as needed
