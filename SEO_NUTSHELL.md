Here are **23 user prompts** designed to exhaustively cover the entire SEO lifecycle for **any given website**. Each prompt aligns with a real-world SEO role or responsibility—content, technical, off-page, analytics, entity-level, edge tactics, and more.

You can run them independently or chain them to build an AI-driven SEO pipeline.

---

### 🧠 STRATEGY & DISCOVERY

1. **Site Diagnosis Prompt**

> “Audit `example.com` and return a full-stack SEO diagnosis across on-page, off-page, technical, UX, and semantic signals. Include baselines, issues, and opportunities by priority.”

2. **Competitor Gap Prompt**

> “Compare `example.com` against `competitor1.com`, `competitor2.com`. Identify keyword gaps, link profile gaps, content themes they dominate, and suggest edge strategies to close the gap.”

3. **Market Intelligence Prompt**

> “Assume `example.com` targets the UK ecommerce market for skincare. What are the macro SEO trends, SERP shifts, and algorithm updates affecting this niche? What pivots would you make?”

---

### ✍️ CONTENT STRATEGY & ON-PAGE

4. **Topical Map Prompt**

> “Create a topical authority map for `example.com` based on its core offerings. Include pillar pages, supporting clusters, interlinking strategy, and missing opportunities.”

5. **On-Page Audit Prompt**

> “For the page `https://example.com/services/cloud-hosting`, do a detailed on-page SEO audit. Include headers, content, keyword use, internal links, schema, and visual engagement.”

6. **Content Refresh Prompt**

> “Find decaying blog content on `example.com` based on estimated age, link velocity, and search intent shift. Recommend refresh instructions and re-optimization plans.”

7. **E-E-A-T Enhancement Prompt**

> “Evaluate how `example.com` demonstrates Experience, Expertise, Authoritativeness, and Trustworthiness. Recommend tactics to boost E-E-A-T signals across the domain.”

---

### 🔧 TECHNICAL SEO

8. **Full Technical Audit Prompt**

> “Perform a technical SEO audit of `example.com`. Check crawling/indexing status, Core Web Vitals, structured data, site architecture, JS rendering, and sitemap issues.”

9. **Core Web Vitals Fix Prompt**

> “LCP and CLS are failing on `example.com/blog/`. Give exact remediation steps by element. Assume site is on WordPress and uses Elementor.”

10. **Indexation Control Prompt**

> “Show me how `example.com` handles crawl budget and indexation. Which pages should be noindexed, canonicalized, or removed from the sitemap?”

11. **Log File Analysis Prompt**

> “Based on sample log data from `example.com`, identify crawl traps, low-value hits, unused content, and suggest a robots.txt or sitemap update strategy.”

---

### 🔗 OFF-PAGE & LINKS

12. **Link Profile Audit Prompt**

> “Analyze the backlink profile of `example.com`. Include anchor diversity, link velocity, spam signals, topical relevance, and recommend link pruning or disavow actions.”

13. **Link Prospecting Prompt**

> “Based on `example.com`’s niche and content themes, generate 30 high-authority link opportunities including format (guest post, HARO, unlinked brand), contact method, and angle.”

14. **Link Acquisition Calendar Prompt**

> “Create a 12-week white/grey-hat link building plan for `example.com` with expected DR growth and safe velocity range. Include fallback tactics and outreach hooks.”

---

### 🏗️ ARCHITECTURE & UX SEO

15. **Site Structure Prompt**

> “Evaluate the URL structure, folder hierarchy, internal link depth, and click path efficiency of `example.com`. Recommend restructuring if needed for better crawl and UX.”

16. **Mobile UX & SERP Behavior Prompt**

> “Simulate user journeys on mobile for `example.com`. Are users pogo-sticking or dropping off? Suggest UX and microcopy changes that improve engagement and reduce bounce.”

---

### 🧾 SCHEMA, ENTITIES, NLP

17. **Schema Injection Prompt**

> “List all schema.org types and properties `example.com` should be using. Include JSON-LD templates for each major content type (product, blog, FAQ, author).”

18. **Entity Optimization Prompt**

> “Evaluate how `example.com` leverages named entities and entity salience in its content. Recommend tweaks to improve NLP parsing and Knowledge Graph alignment.”

---

### 📈 TRACKING & KPI MONITORING

19. **KPI Dashboard Design Prompt**

> “Design a minimal SEO dashboard for `example.com` that monitors traffic quality, keyword rankings, indexation, technical health, link velocity, and cannibalization alerts.”

20. **Change Monitoring Prompt**

> “List the top 10 things that should be monitored daily/weekly/monthly for `example.com`. Suggest how to implement alerts or anomaly detection.”

---

### 🧬 ADVANCED & EDGE TACTICS

21. **Programmatic SEO Prompt**

> “Can `example.com` implement programmatic SEO? If so, suggest scalable content templates, automation stack, and a pilot cluster idea using public data.”

22. **SERP Feature Strategy Prompt**

> “Identify which SERP features (FAQs, snippets, videos, carousels) `example.com` could win. Provide implementation plans and schema or markup needed.”

23. **Risk Ledger Prompt**

> “Create a risk ledger for current SEO strategies of `example.com`. Categorize each by white/grey/black, potential penalty impact, and mitigation fallback.”

---

Let me know if you'd like these grouped into a playbook or output in a CSV for tooling workflows.
