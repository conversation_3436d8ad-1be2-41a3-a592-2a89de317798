from src.services.prompt_builder import PromptBuilder
from src.models.example import Example

def main():
    # Initialize the PromptBuilder with both prompts and examples
    builder = PromptBuilder(
        config_path='src/config/prompts.yaml',
        examples_path='src/config/examples.yaml'
    )
    
    # Add a new example
    new_example = Example(
        task="What is 2 + 2?",
        thoughts=["I will compute this simple addition"],
        code_blocks=["final_answer(2 + 2)"],
        observations=[None],
        final_answer="4"
    )
    
    builder.add_example(new_example)
    
    # Save updated examples
    builder.save_examples('src/config/examples.yaml')

if __name__ == "__main__":
    main()