import yaml
from typing import List, Dict
from pathlib import Path
from src.models.example import Example, ExampleStore

class ExampleManager:
    def __init__(self, examples_path: str = None):
        self.examples = []
        if examples_path:
            self.load_examples(examples_path)

    def load_examples(self, path: str) -> None:
        """Load examples from YAML file"""
        with open(path, 'r') as f:
            examples_data = yaml.safe_load(f)
            for example_data in examples_data['examples']:
                self.examples.append(Example(**example_data))

    def add_example(self, example: Example) -> None:
        """Add a new example"""
        self.examples.append(example)

    def remove_example(self, index: int) -> None:
        """Remove example at specified index"""
        if 0 <= index < len(self.examples):
            self.examples.pop(index)

    def get_formatted_examples(self) -> str:
        """Get all examples formatted for the prompt"""
        return ExampleStore(self.examples).format_all()

    def save_examples(self, path: str) -> None:
        """Save examples to YAML file"""
        examples_data = {
            'examples': [vars(example) for example in self.examples]
        }
        with open(path, 'w') as f:
            yaml.dump(examples_data, f)