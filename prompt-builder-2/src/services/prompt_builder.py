from typing import Any, Dict, Optional
from src.models.prompt import Prompt
from src.services.example_manager import Example, ExampleManager
from src.utils.yaml_loader import load_yaml

class PromptBuilder:
    def __init__(self, config_path: str, examples_path: str = None):
        self.config = load_yaml(config_path)
        self.example_manager = ExampleManager(examples_path)
        self.prompt = self._initialize_prompt()

    def _initialize_prompt(self) -> Prompt:
        # Split the system prompt into pre and post examples sections
        system_prompt = self.config['system_prompt']
        example_split = "Here are a few examples using notional tools:"
        pre_examples, post_examples = system_prompt.split(example_split)

        # Reconstruct the system prompt with current examples
        full_system_prompt = (
            pre_examples + 
            self.example_manager.get_formatted_examples() + 
            post_examples
        )
        
        self.config['system_prompt'] = full_system_prompt
        return Prompt(**self.config)

    def add_example(self, example: Example) -> None:
        """Add a new example and update the prompt"""
        self.example_manager.add_example(example)
        self._initialize_prompt()

    def remove_example(self, index: int) -> None:
        """Remove an example and update the prompt"""
        self.example_manager.remove_example(index)
        self._initialize_prompt()

    def save_examples(self, path: str) -> None:
        """Save current examples to file"""
        self.example_manager.save_examples(path)