examples:
  - task: 'Generate an image of the oldest person in this document.'
    thoughts:
      - 'I will proceed step by step and use the following tools: `document_qa` to find the oldest person in the document, then `image_generator` to generate an image according to the answer.'
    code_blocks:
      - |
        answer = document_qa(document=document, question="Who is the oldest person mentioned?")
        print(answer)
      - |
        image = image_generator("A portrait of <PERSON>, a 55-year-old man living in Canada.")
        final_answer(image)
    observations:
      - 'The oldest person in the document is <PERSON>, a 55 year old lumberjack living in Newfoundland.'
      - null
    final_answer: null

  - task: 'What is the result of the following operation: 5 + 3 + 1294.678?'
    thoughts:
      - 'I will use python code to compute the result of the operation and then return the final answer using the `final_answer` tool'
    code_blocks:
      - |
        result = 5 + 3 + 1294.678
        final_answer(result)
    observations:
      - null
    final_answer: '1302.678'
