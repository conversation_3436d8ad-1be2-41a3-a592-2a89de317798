import yaml
from dataclasses import dataclass
from typing import Any, Dict, Optional
from jinja2 import Template

@dataclass
class PlanningPrompt:
    initial_facts: str
    initial_plan: str
    update_facts: str

@dataclass
class ManagedAgentPrompt:
    task: str
    report: str

@dataclass
class FinalAnswerPrompt:
    pre_messages: str
    post_messages: str

@dataclass
class Prompt:
    system_prompt: str
    planning: PlanningPrompt
    managed_agent: ManagedAgentPrompt
    final_answer: FinalAnswerPrompt

    @classmethod
    def load_from_yaml(cls, yaml_file: str) -> 'Prompt':
        with open(yaml_file, 'r') as file:
            config = yaml.safe_load(file)
            return cls(
                system_prompt=config.get('system_prompt', ''),
                planning=PlanningPrompt(
                    initial_facts=config.get('planning', {}).get('initial_facts', ''),
                    initial_plan=config.get('planning', {}).get('initial_plan', ''),
                    update_facts=config.get('planning', {}).get('update_facts', '')
                ),
                managed_agent=ManagedAgentPrompt(
                    task=config.get('managed_agent', {}).get('task', ''),
                    report=config.get('managed_agent', {}).get('report', '')
                ),
                final_answer=FinalAnswerPrompt(
                    pre_messages=config.get('final_answer', {}).get('pre_messages', ''),
                    post_messages=config.get('final_answer', {}).get('post_messages', '')
                )
            )

    def validate(self) -> bool:
        return bool(self.system_prompt)

    def render_system_prompt(self, **kwargs) -> str:
        template = Template(self.system_prompt)
        return template.render(**kwargs)

    def render_planning(self, **kwargs) -> Dict[str, str]:
        return {
            "initial_facts": Template(self.planning.initial_facts).render(**kwargs),
            "initial_plan": Template(self.planning.initial_plan).render(**kwargs),
            "update_facts": Template(self.planning.update_facts).render(**kwargs)
        }

    def render_managed_agent(self, **kwargs) -> Dict[str, str]:
        return {
            "task": Template(self.managed_agent.task).render(**kwargs),
            "report": Template(self.managed_agent.report).render(**kwargs)
        }

    def render_final_answer(self, **kwargs) -> Dict[str, str]:
        return {
            "pre_messages": Template(self.final_answer.pre_messages).render(**kwargs),
            "post_messages": Template(self.final_answer.post_messages).render(**kwargs)
        }