from dataclasses import dataclass
from typing import List, Optional

@dataclass
class Example:
    task: str
    thoughts: List[str]
    code_blocks: List[str]
    observations: List[Optional[str]]
    final_answer: Optional[str]

    def format(self) -> str:
        formatted = f"Task: \"{self.task}\"\n\n"
        
        for i in range(len(self.thoughts)):
            formatted += f"Thought: {self.thoughts[i]}\n"
            formatted += f"Code:\n```py\n{self.code_blocks[i]}\n```<end_code>\n"
            
            if i < len(self.observations) and self.observations[i]:
                formatted += f"Observation: {self.observations[i]}\n\n"
        
        return formatted

@dataclass
class ExampleStore:
    examples: List[Example]

    def format_all(self) -> str:
        formatted = "Here are a few examples using notional tools:\n---\n"
        for example in self.examples:
            formatted += example.format()
            formatted += "---\n"
        return formatted