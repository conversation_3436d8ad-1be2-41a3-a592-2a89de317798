import unittest
from src.utils.yaml_loader import load_yaml

class TestYamlLoader(unittest.TestCase):

    def test_load_yaml_valid_file(self):
        result = load_yaml('src/config/prompts.yaml')
        self.assertIsInstance(result, dict)
        self.assertIn('system_prompt', result)

    def test_load_yaml_invalid_file(self):
        with self.assertRaises(FileNotFoundError):
            load_yaml('invalid_file.yaml')

    def test_load_yaml_empty_file(self):
        with open('empty.yaml', 'w') as f:
            pass
        with self.assertRaises(ValueError):
            load_yaml('empty.yaml')