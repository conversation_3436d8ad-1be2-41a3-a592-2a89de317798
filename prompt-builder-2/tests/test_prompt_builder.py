import unittest
from src.services.prompt_builder import PromptBuilder
from src.utils.yaml_loader import load_yaml

class TestPromptBuilder(unittest.TestCase):
    def setUp(self):
        self.prompt_config = load_yaml('src/config/prompts.yaml')
        self.prompt_builder = PromptBuilder(self.prompt_config)

    def test_build_prompt(self):
        expected_prompt = self.prompt_config['system_prompt']
        actual_prompt = self.prompt_builder.build_prompt("Test task")
        self.assertEqual(actual_prompt, expected_prompt)

    def test_invalid_task(self):
        with self.assertRaises(ValueError):
            self.prompt_builder.build_prompt("")

if __name__ == '__main__':
    unittest.main()