# Prompt Builder

## Overview

The Prompt Builder is a Python application designed to facilitate the creation and management of prompts for various applications. It leverages a YAML configuration file to define the structure and content of prompts, allowing for easy customization and scalability.

## Features

- Load default prompts from a YAML configuration file.
- Build prompts dynamically based on user-defined parameters.
- Validate and manage prompt structures.

## Project Structure

```
prompt-builder
├── src
│   ├── app.py                # Entry point of the application
│   ├── config
│   │   └── prompts.yaml      # Default prompt configuration
│   ├── models
│   │   ├── __init__.py       # Initializes models package
│   │   └── prompt.py         # Defines the Prompt class
│   ├── services
│   │   ├── __init__.py       # Initializes services package
│   │   └── prompt_builder.py  # Defines the PromptBuilder class
│   └── utils
│       ├── __init__.py       # Initializes utils package
│       └── yaml_loader.py     # Function to load YAML files
├── tests
│   ├── __init__.py           # Initializes tests package
│   ├── test_prompt_builder.py # Unit tests for PromptBuilder
│   └── test_yaml_loader.py    # Unit tests for YAML loader
├── .gitignore                 # Git ignore file
├── README.md                  # Project documentation
├── pyproject.toml             # Project configuration
└── requirements.txt           # Required Python packages
```

## Installation

To install the required dependencies, run:

```
pip install -r requirements.txt
```

## Usage

To run the application, execute the following command:

```
python src/app.py
```

## Contributing

Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.