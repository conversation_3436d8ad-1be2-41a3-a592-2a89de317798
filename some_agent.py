# The prompt for our Ideation specialist
NEXUS_IDEATION_PROMPT = """
You are the "Nexus Ideation Agent," a world-class content strategist. Your sole function is to analyze a given topic and generate a "[Content Cluster Matrix]" containing 5 distinct, high-value content ideas. For each idea, you must provide: a Working Title, a Core Angle, a Target Persona Slice, Strategic Nuances to Explore, a Primary Keyword Opportunity, a Recommended Copy Framework, and a Justification. Your output MUST be only the matrix and nothing else.
"""

# The prompt for our long-form Writing specialist
NEXUS_SYNTHESIS_PROMPT = """
You are the "Nexus Synthesis Agent," a Tier-S Persuasion Architect and Master Copywriter. Your sole function is to take a single, highly-specific content idea (including a title, angle, and framework) and execute it flawlessly. You are required to write comprehensive, authoritative, long-form content of at least 1000 words. You must flesh out every point into detailed, multi-paragraph sections with compelling subheadings and invent illustrative examples or statistics to make abstract concepts tangible. Your output MUST be only the final, formatted article.
"""


from smolagents import CodeAgent, HfApiModel, ToolCallingAgent, LiteLLMModel
import pandas
# A model is required to power the agents
# Replace with a valid model for actual execution
# model = HfApiModel(model_id="google/gemma-2-9b-it") 
model = LiteLLMModel(
    model_id="gemini/gemini-2.0-flash",
    api_key="AIzaSyB4LYkPHFhgc83B0ieV5mpSIBV5wFD-nhE",
    
)
# --- Worker 1: The Ideation Agent ---
nexus_ideation_agent = CodeAgent(
    model=model,
    tools=[],
    system_prompt=NEXUS_IDEATION_PROMPT,
    max_steps=2, # This is a one-shot generation task
)

managed_ideation_agent = ToolCallingAgent(
  model=model,
  tools=[],
    name="generate_content_ideas",
    description="Takes a broad topic and generates a detailed matrix of 5 strategic content angles to pursue. Args: topic (str): The general subject matter, e.g., 'The future of remote work'."
)

# --- Worker 2: The Synthesis (Writing) Agent ---
nexus_synthesis_agent = CodeAgent(
    model=model,
    system_prompt=NEXUS_SYNTHESIS_PROMPT,
    tools=[],
    max_steps=2, # Also a one-shot generation task
)

managed_synthesis_agent = ToolCallingAgent(
    model=model,
    name="write_long_form_article",
    tools=[],
    description="Takes a single, specific content idea from a strategy matrix and writes a comprehensive, 1000+ word article based on it. Args: chosen_idea (str): The full description of the single idea to be written."
)

content_director_agent = CodeAgent(
    model=model,
    managed_agents=[managed_ideation_agent, managed_synthesis_agent],
    # We give the director the ability to use pandas to potentially analyze or present the ideas [1]
    tools=[],
    max_steps=3,
    additional_authorized_imports=["pandas"], 
    system_prompt="You are a Content Director. Your job is to oversee the creation of a single, high-quality content piece. First, use the 'generate_content_ideas' tool to get a list of strategic angles. Then, select the BEST angle from the list and use the 'write_long_form_article' tool to get it written. Finally, present the finished article as your final answer."
)



# The high-level task for the Content Director
task = "I need a pillar content piece on the topic of 'The impact of large language models on the creative industry'."

# Run the director agent, which will in turn call its managed worker agents
final_article = content_director_agent.run(task)

print("\n" + "="*25 + " FINAL PUBLISHED ARTICLE " + "="*25 + "\n")
print(final_article)

# For debugging and verification, we can inspect the director's logs
# This will show the calls to the managed agents [2]
print("\n" + "="*25 + " DIRECTOR'S LOGS (INNER MEMORY) " + "="*25 + "\n")
print(content_director_agent.prompt_templates)