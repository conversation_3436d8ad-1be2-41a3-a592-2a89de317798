import requests
import time
from collections import defaultdict
import itertools
import os

# --- Configuration & Mock API Setup ---

# In a real application, use os.environ.get('API_KEY') to securely access your key
# For this example, we'll leave it blank as our mock functions don't require it.
API_KEY = "" 

# This is a mock database. In a real scenario, this data would come from live API calls.
# It simulates the keywords that various URLs rank for.
MOCK_URL_TO_KEYWORDS_DB = {
    "https://example.com/home-charging-install": ["home ev charging installation", "cost to install level 2 charger", "240v car charger installation", "electrician for ev charger", "ev charger installation cost"],
    "https://example.com/ev-charging-levels": ["level 1 vs level 2 charging", "what is dc fast charging", "ev charging speed comparison", "types of ev chargers", "electric car charging levels"],
    "https://example.com/public-chargers": ["ev charging stations near me", "public ev chargers", "chargepoint vs electrify america", "how to use a public ev charger", "find ev chargers"],
    "https://example.com/charging-costs": ["how much does it cost to charge an ev", "ev charging cost vs gas", "is it cheaper to charge an ev at home", "ev electricity cost calculator"],
    "https://example.com/best-chargers-2025": ["best home ev charger 2025", "level 2 charger reviews", "top rated car chargers", "wallbox vs juicebox", "best ev home charger"],
    "https://example.com/generic-charging-guide": ["electric car charging", "ev charging guide", "how to charge an electric car", "ev charging basics"]
}

# This mock database simulates the top 10 SERP results for a given keyword.
MOCK_SERP_DB = {
    "electric car charging": ["https://example.com/generic-charging-guide", "https://example.com/ev-charging-levels", "https://example.com/public-chargers", "https://example.com/charging-costs"],
    "home ev charging installation": ["https://example.com/home-charging-install", "https://example.com/generic-charging-guide", "https://some-electrician.com/install", "https://diy.com/ev-install"],
    "cost to install level 2 charger": ["https://example.com/home-charging-install", "https://some-electrician.com/install", "https:// Forbes.com/costs", "https://cnet.com/reviews"],
    "level 1 vs level 2 charging": ["https://example.com/ev-charging-levels", "https://example.com/generic-charging-guide", "https://caranddriver.com/levels", "https://motor-biscuit.com/explain"],
    "ev charging stations near me": ["https://example.com/public-chargers", "https://plugshare.com", "https://chargepoint.com/map", "https://google.com/maps"],
    "how much does it cost to charge an ev": ["https://example.com/charging-costs", "https://example.com/generic-charging-guide", "https://nerdwallet.com/ev-cost", "https://www.forbes.com/advisor/home-improvement/cost-to-install-ev-charger/"],
    "best home ev charger 2025": ["https://example.com/best-chargers-2025", "https://cnet.com/top-chargers", "https://caranddriver.com/best-ev-chargers", "https://wired.com/reviews"]
}


# --- Phase 1: Keyword Expansion & Idea Generation ---

def phase_1_1_expand_keywords(seed_keyword: str) -> list[str]:
    """
    Simulates calling an API to get related keywords.
    In a real scenario, this would call Semrush, Ahrefs, etc.
    """
    print(f"STEP 1.1: Expanding keywords for '{seed_keyword}'...")
    # Mock Response: In a real app, you would parse a JSON response here.
    mock_api_response = [
        "ev charging stations", "home car charger", "cost to install car charger",
        "public ev chargers", "level 2 charger", "fast ev charger",
        "electric car charging", "home ev charging installation", "cost to install level 2 charger",
        "level 1 vs level 2 charging", "ev charging stations near me",
        "how much does it cost to charge an ev", "best home ev charger 2025"
    ]
    # Adding the seed keyword to ensure it's in the list
    if seed_keyword not in mock_api_response:
        mock_api_response.append(seed_keyword)
    return list(set(mock_api_response)) # Use set to remove duplicates

def phase_1_2_discover_questions(seed_keyword: str) -> list[str]:
    """
    Simulates calling an API like AlsoAsked to get "People Also Ask" questions.
    """
    print(f"STEP 1.2: Discovering questions related to '{seed_keyword}'...")
    # Mock Response:
    mock_api_response = [
        "How much does it cost to charge an electric car?",
        "Can you plug an electric car into a regular outlet?",
        "How long does it take to charge an electric car?",
        "What are the different levels of EV charging?"
    ]
    return mock_api_response

# --- Phase 2: Competitive & SERP Analysis ---

def phase_2_1_get_top_urls_for_keyword(keyword: str) -> list[str]:
    """
    Simulates a SERP API call to get the top 10 ranking URLs for a keyword.
    
    Abstracted Implementation:
    - Replace the MOCK_SERP_DB lookup with a real API call.
    - Example with `requests` to a hypothetical SERP API:
      endpoint = f"https://api.serpprovider.com/search?q={keyword}&key={API_KEY}"
      response = requests.get(endpoint)
      urls = [item['url'] for item in response.json()['organic_results']]
      return urls
    """
    # print(f"  - Fetching SERP for: '{keyword}'") # Uncomment for verbose logging
    # Adding a small delay to simulate network latency of a real API call
    time.sleep(0.05) 
    return MOCK_SERP_DB.get(keyword, [])

# This function is not directly used in this script's flow but is conceptually part of the workflow.
# It's represented by the `MOCK_URL_TO_KEYWORDS_DB`.
def phase_2_2_get_keywords_for_urls(urls: list[str]) -> dict:
    """
    Simulates calling an API to find all keywords a list of URLs ranks for.
    This is conceptually what a "Keyword Gap" or "Content Gap" analysis does.
    """
    print("STEP 2.2: Conceptually gathering keywords for top URLs (using mock DB)...")
    results = defaultdict(list)
    for url in urls:
        if url in MOCK_URL_TO_KEYWORDS_DB:
            results[url].extend(MOCK_URL_TO_KEYWORDS_DB[url])
    return dict(results)


# --- Phase 3: Clustering and Intent Mapping ---

def phase_3_1_cluster_keywords_by_serp(keywords: list[str], min_overlap: int = 3) -> list[list[str]]:
    """
    Groups keywords based on the number of shared URLs in their top 10 search results.
    This is the core of the SERP-based clustering logic.
    """
    print(f"\nSTEP 3.1: Clustering {len(keywords)} keywords with a minimum SERP overlap of {min_overlap}...")
    
    # A dictionary to cache SERP results to avoid redundant API calls
    serp_cache = {}
    
    # Fetch SERPs for all keywords first
    for keyword in keywords:
        if keyword not in serp_cache:
            serp_cache[keyword] = set(phase_2_1_get_top_urls_for_keyword(keyword))

    clusters = []
    processed_keywords = set()

    for keyword_a in keywords:
        if keyword_a in processed_keywords:
            continue
        
        current_cluster = [keyword_a]
        processed_keywords.add(keyword_a)
        
        serp_a = serp_cache.get(keyword_a, set())
        if not serp_a:
            continue

        for keyword_b in keywords:
            if keyword_b in processed_keywords or keyword_b == keyword_a:
                continue

            serp_b = serp_cache.get(keyword_b, set())
            if not serp_b:
                continue

            # Calculate the number of overlapping URLs
            overlap = len(serp_a.intersection(serp_b))

            if overlap >= min_overlap:
                current_cluster.append(keyword_b)
                processed_keywords.add(keyword_b)
        
        clusters.append(current_cluster)
        
    print(f"-> Found {len(clusters)} clusters.")
    return clusters

def phase_3_2_assign_cluster_names_and_intent(clusters: list[list[str]]) -> list[dict]:
    """
    Processes raw keyword clusters to assign a representative name and infer user intent.
    """
    print("\nSTEP 3.2: Assigning cluster names and inferring intent...")
    structured_clusters = []
    
    intent_map = {
        'informational': ['how', 'what', 'when', 'where', 'why', 'guide', 'resource', 'tips', 'ideas', 'learn'],
        'commercial': ['best', 'top', 'review', 'comparison', 'vs', 'alternative'],
        'transactional': ['buy', 'price', 'cost', 'sale', 'cheap', 'discount'],
        'navigational': [] # Usually brand names, harder to infer automatically
    }

    for cluster in clusters:
        if not cluster:
            continue
            
        # Heuristic for cluster name: shortest, most representative keyword
        # Often the shortest keyword is the "head" term of the topic.
        cluster.sort(key=len)
        cluster_name = cluster[0]
        
        # Infer intent
        inferred_intent = "Informational" # Default
        for intent, triggers in intent_map.items():
            if any(trigger in cluster_name.lower() for trigger in triggers):
                inferred_intent = intent.capitalize()
                break
        
        structured_clusters.append({
            "cluster_name": cluster_name,
            "primary_intent": inferred_intent,
            "keywords": cluster
        })
        
    print(f"-> Structured {len(structured_clusters)} clusters.")
    return structured_clusters

# --- Main Workflow Execution ---

def main():
    """
    Main function to orchestrate the entire SEO clustering workflow.
    """
    seed_keyword = "electric car charging"
    
    # --- Phase 1 ---
    expanded_keywords = phase_1_1_expand_keywords(seed_keyword)
    questions = phase_1_2_discover_questions(seed_keyword)
    
    # Combine all discovered keywords into one master list
    master_keyword_list = list(set(expanded_keywords + questions))
    print(f"\n-> Master list contains {len(master_keyword_list)} unique keywords.")
    
    # --- Phase 3 ---
    # In this workflow, we skip directly calling phase_2 functions and embed their logic
    # into the clustering step, which is more efficient.
    raw_clusters = phase_3_1_cluster_keywords_by_serp(master_keyword_list, min_overlap=2)
    final_clusters = phase_3_2_assign_cluster_names_and_intent(raw_clusters)
    
    # --- Final Output ---
    print("\n--- SEO Keyword Clustering Results ---")
    for item in final_clusters:
        print(f"\nCluster Name:   {item['cluster_name']}")
        print(f"Primary Intent: {item['primary_intent']}")
        print("Keywords:")
        for kw in item['keywords']:
            print(f"  - {kw}")
    print("\n--- End of Workflow ---")

if __name__ == "__main__":
    main()
