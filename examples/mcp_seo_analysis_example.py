#!/usr/bin/env python3
"""
Example: Comprehensive SEO Analysis using DataForSEO MCP Integration
This example demonstrates how to use the enhanced SEO agent with MCP tools
"""

import sys
import os

# Add the parent directory to the path so we can import from the main app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import agent

def comprehensive_seo_analysis():
    """Perform a comprehensive SEO analysis using MCP tools"""
    
    print("🚀 Starting Comprehensive SEO Analysis with MCP Integration")
    print("=" * 70)
    
    # Example 1: Keyword Research and SERP Analysis
    print("\n📊 Example 1: Keyword Research and SERP Analysis")
    print("-" * 50)
    
    query1 = """
    Perform a comprehensive keyword research and SERP analysis for the topic 'content marketing':
    
    1. Use the MCP keyword research tool to find related keywords for 'content marketing'
    2. Use the MCP SERP analysis tool to analyze the top 3 keywords from the research
    3. Identify opportunities and provide recommendations
    4. Format the results in a clear, actionable report
    """
    
    try:
        result1 = agent.run(query1)
        print("✅ Keyword Research and SERP Analysis completed")
        print(f"Result: {result1}")
    except Exception as e:
        print(f"❌ Error in Example 1: {e}")
    
    print("\n" + "=" * 70)
    
    # Example 2: Competitor Analysis
    print("\n🔍 Example 2: Competitor Analysis")
    print("-" * 50)
    
    query2 = """
    Perform a detailed competitor analysis for the domain 'hubspot.com':
    
    1. Use the MCP competitor analysis tool to analyze hubspot.com
    2. Identify their top-performing keywords
    3. Analyze their backlink profile
    4. Provide insights on their SEO strategy
    5. Suggest opportunities for competing against them
    """
    
    try:
        result2 = agent.run(query2)
        print("✅ Competitor Analysis completed")
        print(f"Result: {result2}")
    except Exception as e:
        print(f"❌ Error in Example 2: {e}")
    
    print("\n" + "=" * 70)
    
    # Example 3: Multi-Domain Comparison
    print("\n⚖️ Example 3: Multi-Domain Comparison")
    print("-" * 50)
    
    query3 = """
    Compare the SEO performance of three marketing tool websites:
    
    1. Use MCP competitor analysis for each domain: semrush.com, ahrefs.com, moz.com
    2. Compare their keyword rankings and backlink profiles
    3. Identify the strengths and weaknesses of each
    4. Provide recommendations for improving against these competitors
    5. Create a comparison table with key metrics
    """
    
    try:
        result3 = agent.run(query3)
        print("✅ Multi-Domain Comparison completed")
        print(f"Result: {result3}")
    except Exception as e:
        print(f"❌ Error in Example 3: {e}")
    
    print("\n" + "=" * 70)

def targeted_keyword_analysis():
    """Perform targeted keyword analysis for specific industries"""
    
    print("\n🎯 Targeted Keyword Analysis for E-commerce")
    print("-" * 50)
    
    query = """
    Perform a targeted keyword analysis for an e-commerce website selling fitness equipment:
    
    1. Use MCP keyword research for these seed keywords: 'home gym equipment', 'fitness machines', 'workout gear'
    2. Use MCP SERP analysis for the top 5 keywords with highest search volume
    3. Identify long-tail keyword opportunities
    4. Analyze SERP features (shopping results, featured snippets, etc.)
    5. Provide a content strategy based on the findings
    6. Suggest product page optimization opportunities
    """
    
    try:
        result = agent.run(query)
        print("✅ Targeted Keyword Analysis completed")
        print(f"Result: {result}")
    except Exception as e:
        print(f"❌ Error in Targeted Analysis: {e}")

def local_seo_analysis():
    """Perform local SEO analysis"""
    
    print("\n📍 Local SEO Analysis")
    print("-" * 50)
    
    query = """
    Perform a local SEO analysis for a dental practice:
    
    1. Use MCP keyword research for local dental keywords like 'dentist near me', 'dental clinic', 'teeth cleaning'
    2. Use MCP SERP analysis for these keywords in different locations (New York, Los Angeles, Chicago)
    3. Identify local pack opportunities
    4. Analyze local competition
    5. Provide local SEO recommendations
    6. Suggest content ideas for local relevance
    """
    
    try:
        result = agent.run(query)
        print("✅ Local SEO Analysis completed")
        print(f"Result: {result}")
    except Exception as e:
        print(f"❌ Error in Local SEO Analysis: {e}")

def content_gap_analysis():
    """Perform content gap analysis using MCP tools"""
    
    print("\n📝 Content Gap Analysis")
    print("-" * 50)
    
    query = """
    Perform a content gap analysis for a SaaS company in the project management space:
    
    1. Use MCP competitor analysis on these domains: asana.com, trello.com, monday.com
    2. Use MCP keyword research for 'project management software', 'team collaboration tools', 'task management'
    3. Identify keywords that competitors rank for but are missing from our analysis
    4. Find content opportunities with low competition but good search volume
    5. Suggest a content calendar based on the gaps identified
    6. Prioritize content ideas by potential impact
    """
    
    try:
        result = agent.run(query)
        print("✅ Content Gap Analysis completed")
        print(f"Result: {result}")
    except Exception as e:
        print(f"❌ Error in Content Gap Analysis: {e}")

def main():
    """Main function to run all examples"""
    
    print("🔧 DataForSEO MCP Integration Examples")
    print("=" * 70)
    print("This script demonstrates various SEO analysis scenarios using MCP tools")
    print("Make sure you have:")
    print("1. Set up DataForSEO MCP server (run setup_dataforseo_mcp.py)")
    print("2. Configured your DataForSEO credentials")
    print("3. Installed all required dependencies")
    print("=" * 70)
    
    # Check if user wants to proceed
    proceed = input("\nDo you want to proceed with the examples? (y/n): ").lower().strip()
    
    if proceed != 'y':
        print("Exiting...")
        return
    
    try:
        # Run comprehensive analysis
        comprehensive_seo_analysis()
        
        # Ask if user wants to continue with more examples
        continue_examples = input("\nDo you want to run additional targeted examples? (y/n): ").lower().strip()
        
        if continue_examples == 'y':
            # Run targeted analyses
            targeted_keyword_analysis()
            local_seo_analysis()
            content_gap_analysis()
        
        print("\n🎉 All examples completed successfully!")
        print("\n💡 Tips for using MCP tools in your own projects:")
        print("1. Always specify clear, detailed queries for better results")
        print("2. Combine multiple MCP tools for comprehensive analysis")
        print("3. Use location-specific analysis for local SEO")
        print("4. Regular competitor monitoring using MCP tools")
        print("5. Integrate findings into your content and SEO strategy")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check your setup and try again")

if __name__ == "__main__":
    main()
