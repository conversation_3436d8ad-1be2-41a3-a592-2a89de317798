"""
Example usage of the keyword research tools
Demonstrates a complete workflow for keyword research
"""

from tools.crawl_and_extract_keywords import crawl_and_extract_keywords
from tools.identify_keyword_gaps import identify_keyword_gaps
from tools.cluster_keywords import cluster_keywords
from tools.get_keyword_metrics import get_keyword_metrics
from tools.dataforseo_keyword_research import dataforseo_keyword_research
import json

def run_keyword_research_example():
    """Run a complete keyword research workflow example"""
    
    print("Starting keyword research workflow...")
    
    # Step 1: Initial keyword research with seed keywords
    print("\n1. Performing initial keyword research...")
    seed_keywords = ["content marketing", "blog strategy"]
    initial_research = dataforseo_keyword_research(
        seed_keywords=seed_keywords,
        location="US"
    )
    print(f"Found {len(initial_research['expanded_keywords'])} potential keywords")
    print(f"Identified {len(initial_research['topics'])} main topics")
    
    # Step 2: Analyze competitor URLs
    print("\n2. Analyzing competitor URLs...")
    user_url = "https://example.com/blog"
    competitor_urls = [
        "https://competitor1.com/blog",
        "https://competitor2.com/content-marketing"
    ]
    
    keyword_data = crawl_and_extract_keywords(
        user_url=user_url,
        competitor_urls=competitor_urls
    )
    print(f"Extracted {keyword_data['extraction_summary']['user_keywords_count']} keywords from user URL")
    print(f"Extracted {keyword_data['extraction_summary']['competitor_keywords_count']} keywords from competitor URLs")
    
    # Step 3: Identify keyword gaps
    print("\n3. Identifying keyword gaps...")
    gaps = identify_keyword_gaps(
        user_keywords=keyword_data['user_keywords'],
        competitor_keywords=[kw for comp in keyword_data['competitor_data'].values() for kw in comp]
    )
    print(f"Found {len(gaps['keyword_gaps'])} keyword opportunities")
    print(f"User has {len(gaps['user_advantages'])} unique keywords")
    
    # Step 4: Cluster keywords
    print("\n4. Clustering keywords...")
    # Combine all keywords for clustering
    all_keywords = []
    all_keywords.extend(initial_research['expanded_keywords'])
    all_keywords.extend(keyword_data['user_keywords'])
    for comp_keywords in keyword_data['competitor_data'].values():
        all_keywords.extend(comp_keywords)
    
    clusters = cluster_keywords(all_keywords)
    print(f"Created {clusters['cluster_summary']['total_clusters']} keyword clusters")
    print(f"Average {clusters['cluster_summary']['avg_keywords_per_cluster']} keywords per cluster")
    
    # Step 5: Get detailed metrics for top opportunities
    print("\n5. Getting metrics for top opportunities...")
    top_keywords = [
        gap['keyword'] for gap in gaps['keyword_gaps'][:10]  # Top 10 gaps
    ]
    metrics = get_keyword_metrics(top_keywords, location="US")
    print(f"Analyzed metrics for {len(metrics['keyword_metrics'])} priority keywords")
    
    # Generate final report
    report = {
        'summary': {
            'total_keywords_analyzed': len(all_keywords),
            'keyword_gaps_found': len(gaps['keyword_gaps']),
            'keyword_clusters': clusters['cluster_summary']['total_clusters'],
            'top_opportunities': len(top_keywords)
        },
        'keyword_opportunities': {
            'gaps': gaps['keyword_gaps'][:5],  # Top 5 gaps
            'clusters': [
                {
                    'theme': cluster['theme'],
                    'keywords': cluster['primary_keywords']
                }
                for cluster in clusters['clusters'][:3]  # Top 3 clusters
            ],
            'metrics': {
                keyword: metrics['keyword_metrics'][keyword]
                for keyword in top_keywords[:5]  # Top 5 keywords with metrics
            }
        },
        'competitor_analysis': {
            'total_competitor_keywords': keyword_data['extraction_summary']['competitor_keywords_count'],
            'user_unique_keywords': len(gaps['user_advantages']),
            'competitor_unique_keywords': len(gaps['keyword_gaps'])
        },
        'recommendations': {
            'quick_wins': [
                gap['keyword'] for gap in gaps['keyword_gaps']
                if gap['opportunity_level'] == 'high'
            ][:3],
            'strategic_opportunities': [
                cluster['theme'] for cluster in clusters['clusters']
                if len(cluster['primary_keywords']) >= 3
            ][:3]
        }
    }
    
    # Save report to file
    with open('keyword_research_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print("\nKeyword research workflow completed!")
    print("Report saved to 'keyword_research_report.json'")
    return report

if __name__ == "__main__":
    report = run_keyword_research_example()
    print("\nExample Report Preview:")
    print(json.dumps(report['summary'], indent=2))
