#!/usr/bin/env python3
"""
Comprehensive Keyword Research Workflow Example
Demonstrates the complete MCP-based keyword research system with intelligent endpoint selection
"""

import sys
import os

# Add the parent directory to the path so we can import from the main app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import agent

def demonstrate_workflow_steps():
    """Demonstrate each step of the keyword research workflow"""
    
    print("🚀 Comprehensive Keyword Research Workflow with MCP Integration")
    print("=" * 80)
    
    # Example 1: Complete Workflow
    print("\n📊 Example 1: Complete Keyword Research Workflow")
    print("-" * 60)
    
    query1 = """
    Perform a comprehensive keyword research workflow for the website 'hassconsult.com':
    
    1. Use the comprehensive_keyword_research tool to run the complete workflow
    2. Analyze the website content to understand the business
    3. Generate 500 relevant keywords using intelligent endpoint selection
    4. Cluster the keywords into pillar topics
    5. Provide a detailed content strategy with recommendations
    
    Please provide a structured analysis with:
    - Business analysis summary
    - Keyword expansion results
    - Pillar topics identified
    - Content strategy recommendations
    - Next steps for implementation
    """
    
    try:
        result1 = agent.run(query1)
        print("✅ Complete workflow executed successfully")
        print(f"Result: {result1}")
    except Exception as e:
        print(f"❌ Error in Complete Workflow: {e}")
    
    print("\n" + "=" * 80)
    
    # Example 2: Step-by-Step Workflow
    print("\n🔍 Example 2: Step-by-Step Keyword Research")
    print("-" * 60)
    
    query2 = """
    Perform step-by-step keyword research for a SaaS project management tool:
    
    Step 1: Use analyze_website_content for 'asana.com' to understand the business context
    Step 2: Use expand_seed_keywords to generate 300 keywords from the seed keywords
    Step 3: Use cluster_and_analyze_keywords to organize the keywords into clusters
    
    For each step, explain:
    - What endpoint was selected and why
    - The results obtained
    - How it feeds into the next step
    """
    
    try:
        result2 = agent.run(query2)
        print("✅ Step-by-step workflow completed")
        print(f"Result: {result2}")
    except Exception as e:
        print(f"❌ Error in Step-by-Step Workflow: {e}")
    
    print("\n" + "=" * 80)

def demonstrate_intelligent_endpoint_selection():
    """Demonstrate intelligent endpoint selection based on requirements"""
    
    print("\n🧠 Intelligent Endpoint Selection Examples")
    print("-" * 60)
    
    # Example 3: Small Scale Keyword Expansion
    print("\n📈 Example 3: Small Scale Keyword Expansion (< 100 keywords)")
    
    query3 = """
    Perform keyword expansion for a local bakery with these requirements:
    - Seed keywords: ['bakery', 'fresh bread', 'pastries']
    - Target: 50 keywords
    - Location: 'New York, United States'
    
    Use expand_seed_keywords and explain:
    1. Which DataForSEO endpoint was automatically selected
    2. Why that endpoint was chosen for this scale
    3. The expansion results and metrics
    """
    
    try:
        result3 = agent.run(query3)
        print("✅ Small scale expansion completed")
        print(f"Result: {result3}")
    except Exception as e:
        print(f"❌ Error in Small Scale Expansion: {e}")
    
    # Example 4: Large Scale Keyword Expansion
    print("\n📊 Example 4: Large Scale Keyword Expansion (1000+ keywords)")
    
    query4 = """
    Perform large-scale keyword expansion for an e-commerce fitness equipment store:
    - Seed keywords: ['home gym', 'fitness equipment', 'workout gear', 'exercise machines']
    - Target: 1000 keywords
    - Location: 'United States'
    
    Use expand_seed_keywords and analyze:
    1. Which endpoint was selected for this larger scale
    2. The expansion strategy used
    3. Quality and relevance of generated keywords
    4. Clustering potential of the results
    """
    
    try:
        result4 = agent.run(query4)
        print("✅ Large scale expansion completed")
        print(f"Result: {result4}")
    except Exception as e:
        print(f"❌ Error in Large Scale Expansion: {e}")

def demonstrate_advanced_clustering():
    """Demonstrate advanced keyword clustering and pillar topic creation"""
    
    print("\n🎯 Advanced Clustering and Pillar Topic Creation")
    print("-" * 60)
    
    query5 = """
    Demonstrate advanced keyword clustering for a digital marketing agency:
    
    1. Start with comprehensive_keyword_research for 'hubspot.com'
    2. Focus on the clustering results and pillar topics
    3. Analyze the content strategy recommendations
    4. Provide insights on:
       - How keywords were grouped into clusters
       - Intent distribution across clusters
       - Pillar topic prioritization
       - Content calendar suggestions
       - Internal linking opportunities
    """
    
    try:
        result5 = agent.run(query5)
        print("✅ Advanced clustering analysis completed")
        print(f"Result: {result5}")
    except Exception as e:
        print(f"❌ Error in Advanced Clustering: {e}")

def demonstrate_competitor_integration():
    """Demonstrate competitor analysis integration with keyword research"""
    
    print("\n⚔️ Competitor Analysis Integration")
    print("-" * 60)
    
    query6 = """
    Perform integrated competitor and keyword analysis:
    
    1. Use mcp_competitor_analysis for 'semrush.com'
    2. Use comprehensive_keyword_research for the same domain
    3. Compare and analyze:
       - Competitor's top-ranking keywords
       - Keyword opportunities they're missing
       - Content gaps in their strategy
       - Recommendations for competing against them
    
    Provide a strategic analysis combining both datasets.
    """
    
    try:
        result6 = agent.run(query6)
        print("✅ Competitor integration analysis completed")
        print(f"Result: {result6}")
    except Exception as e:
        print(f"❌ Error in Competitor Integration: {e}")

def main():
    """Main function to run all examples"""
    
    print("🔧 MCP-Based Keyword Research Workflow Examples")
    print("=" * 80)
    print("This script demonstrates the comprehensive keyword research system with:")
    print("✓ Intelligent DataForSEO endpoint selection")
    print("✓ Complete workflow automation")
    print("✓ Advanced keyword clustering")
    print("✓ Pillar topic creation")
    print("✓ Content strategy development")
    print("=" * 80)
    
    # Check if user wants to proceed
    proceed = input("\nDo you want to proceed with the workflow examples? (y/n): ").lower().strip()
    
    if proceed != 'y':
        print("Exiting...")
        return
    
    try:
        # Run workflow demonstrations
        demonstrate_workflow_steps()
        
        # Ask if user wants to continue with advanced examples
        continue_examples = input("\nDo you want to run advanced examples? (y/n): ").lower().strip()
        
        if continue_examples == 'y':
            demonstrate_intelligent_endpoint_selection()
            demonstrate_advanced_clustering()
            demonstrate_competitor_integration()
        
        print("\n🎉 All workflow examples completed successfully!")
        print("\n💡 Key Benefits of the MCP-Based System:")
        print("1. ✅ Intelligent endpoint selection based on requirements")
        print("2. ✅ Automated workflow from content analysis to strategy")
        print("3. ✅ Advanced clustering with intent analysis")
        print("4. ✅ Pillar topic identification and prioritization")
        print("5. ✅ Comprehensive content strategy recommendations")
        print("6. ✅ Seamless integration with competitor analysis")
        
        print("\n🚀 Next Steps:")
        print("1. Implement the workflow in your SEO projects")
        print("2. Customize clustering algorithms for your industry")
        print("3. Integrate with content management systems")
        print("4. Set up automated keyword monitoring")
        print("5. Develop custom pillar page templates")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check your setup and try again")

if __name__ == "__main__":
    main()
