
# --- 1. The Agent's Tools Library (tools.py) ---
import json
import logging
from typing import Any, Dict, List

import numpy as np


class VectorizationService:
    """A simple service to convert text into numerical vectors."""
    def __init__(self):
        self.vocab = {
            "seo": [0.1, 0.2, 0.3], "content": [0.2, 0.3, 0.4], "ev": [0.9, 0.1, 0.1],
            "charger": [0.8, 0.2, 0.1], "cost": [0.4, 0.5, 0.6], "install": [0.7, 0.3, 0.2],
            "planning": [0.3, 0.6, 0.8], "road": [0.5, 0.5, 0.1], "trip": [0.6, 0.7, 0.9]
        }
        self.vector_size = 3

    def vectorize(self, text: str) -> List[float]:
        words = text.lower().split()
        vectors = [self.vocab.get(word, np.zeros(self.vector_size)) for word in words]
        if not vectors: return list(np.zeros(self.vector_size))
        return list(np.mean(vectors, axis=0))



class VectorDBTool:
    """A tool for managing and searching a vector database for semantic context."""
    def __init__(self, db_path='vector_db.json'):
        self.db_path = db_path
        self.vectorizer = VectorizationService()
        self.index = self._load_index()

    def _load_index(self):
        try:
            with open(self.db_path, 'r') as f:
                logging.info(f"VECTOR DB: Index loaded from {self.db_path}")
                return json.load(f)
        except FileNotFoundError:
            logging.info("VECTOR DB: No index file found. Initializing new index.")
            return []
    
    def _save_index(self):
        with open(self.db_path, 'w') as f: json.dump(self.index, f, indent=2)
    
    @staticmethod
    def _cosine_similarity(v1, v2):
        v1, v2 = np.array(v1), np.array(v2)
        dot = np.dot(v1, v2)
        norm = np.linalg.norm(v1) * np.linalg.norm(v2)
        return dot / norm if norm != 0 else 0.0
    
    def add(self, text_content: str, metadata: Dict[str, Any]):
        vector = self.vectorizer.vectorize(text_content)
        self.index.append({"vector": vector, "metadata": metadata})
        self._save_index()
    
    def search(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
        query_vector = self.vectorizer.vectorize(query_text)
        if not self.index: return []
        results = [{"similarity": self._cosine_similarity(query_vector, item['vector']), "metadata": item['metadata']} for item in self.index]
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]



