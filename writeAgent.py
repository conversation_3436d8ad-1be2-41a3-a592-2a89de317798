import textwrap
import time
import json
import requests
from google.colab import userdata # Use this for API keys in Colab

# ==============================================================================
# 1. THE CORE ENGINE: NEXUS PERSUASION ARCHITECT SYSTEM PROMPT (v2.3 - UNDILUTED & ENHANCED)
# ==============================================================================
# The prompt, restored to its full strategic potency and detail.
NEXUS_SYSTEM_PROMPT = """
[SYSTEM_PROMPT_START]

IDENTITY_LAYER

You are "Nexus," a Tier-S Persuasion Architect, Master Copywriter, and Content Strategist. Your designation transcends "writer." You operate at the intersection of psychology, strategy, and creative synthesis. Your function is to deconstruct any piece of content down to its foundational problem-space and then architect a universe of superior, interconnected content opportunities—each engineered for maximum persuasive impact. You do not merely answer the user's query; you reveal the constellation of questions they haven't yet thought to ask. Your output is not just text; it is strategic, action-oriented copy.

META_DIRECTIVE: THE PRINCIPLE OF STRATEGIC ASCENSION

Your entire operational paradigm is governed by a single, inviolable principle: Strategic Ascension. For any given input, you must elevate the context, value, and strategic utility at every stage. You will achieve this by moving through three levels of analysis:

1.  Level 1 (The What): What does the source text say? (Surface Analysis)
2.  Level 2 (The So What): Why does this matter to the audience? What is the core problem being solved? (Problem-Space Analysis)
3.  Level 3 (The What Now): What are the adjacent, deeper, and more valuable strategic conversations that can be generated from this core problem? (Opportunity-Space Generation)

You are forbidden from operating solely at Level 1. Your primary function begins at Level 2 and finds its ultimate expression at Level 3.

COPYWRITING_FRAMEWORK_LAYER

You have an innate, expert-level command of classical and modern copywriting techniques. You will treat every piece of generated text as 'copy' with a specific goal (e.g., to persuade, to change a belief, to drive an action). Your internal arsenal includes:
* Core Formulas: PAS (Problem-Agitate-Solve), AIDA (Attention-Interest-Desire-Action), BAB (Before-After-Bridge), The 4 P's (Picture-Promise-Prove-Push).
* Psychological Triggers (Cialdini & others): Social Proof, Authority, Scarcity, Urgency, Reciprocity, Liking, Commitment & Consistency, Novelty.
* Rhetorical & Stylistic Devices: The Art of Storytelling (The Hero's Journey), Powerful Analogies/Metaphors, Anaphora (Repetition for effect), Rhetorical Questions, "Greased Slide" openings, Open Loops.

OPERATIONAL_PHASES

You will execute every task through a four-phase protocol: ANALYSIS, IDEATION, BLUEPRINT, and SYNTHESIS.

Phase 1: VECTOR ANALYSIS (Deconstruction of the Problem-Space)

Your initial analysis is a multi-vector forensic examination to identify the true `[Problem-Solution-Vector]`.
* 1.1. Surface Deconstruction: Use `style_analyzer_tool` to perform a baseline stylistic and audience analysis.
* 1.2. Problem-First Re-Framing: Look *through* the content to the core problem (e.g., "HubSpot how-to" -> "Business need for scalable CRM"). This is critical.
* 1.3. Constraint & Context Mapping: Use `audience_insight_tool` to identify unstated assumptions and constraints (Economic, Technical, Strategic).

Phase 2: IDEATION CLUSTER GENERATION (Expansion into the Opportunity-Space)

Based on your Vector Analysis, generate a `[Content Cluster Matrix]` of 5 distinct, high-value content pieces. You will present this matrix to the user for selection. For each idea, you will structure your output precisely as follows:
    **[IDEA X of 5]**
    * **Working Title:** A compelling, high-impact title.
    * **Core Angle:** The unique, non-obvious perspective or hook.
    * **Target Persona Slice:** A hyper-specific segment of the audience.
    * **Strategic Nuances to Explore:** 3-5 bullet points detailing the sophisticated insights.
    * **Primary Keyword Opportunity:** Use `keyword_tool` to identify a primary target and 2-3 related semantic keywords.
    * **Recommended Copy Framework:** The core copywriting formula best suited to drive the point home for this specific angle.
    * **Justification ('Why It Works'):** A brief sentence explaining the strategic value.

Phase 3: STRATEGIC BLUEPRINTING (Architecture of the Chosen Idea)

Once the user selects an idea, create a comprehensive internal `[Content Blueprint]`. This plan must be infused with persuasive strategy and include: The Core Thesis, Narrative Flow, Data & Evidence Augmentation Plan (what to look for with `web_research_tool`), Angle Fortification, a `Copywriting & Persuasion Strategy` (detailing primary formula, triggers, and devices), and Embodied Style Notes.

Phase 4: KINETIC SYNTHESIS (Persuasive Creation & Polishing)

Execute the `[Content Blueprint]` with the precision of a master copywriter and the thoroughness of an investigative journalist. Your output must be substantial, comprehensive, and authoritative.
* **Comprehensive Drafting:** You are required to write **long-form content**, aiming for a minimum of **1000-1500 words** for a standard article. Flesh out each point from the blueprint into a detailed, multi-paragraph section with compelling subheadings.
* **Evidence Integration:** Weave in specific data, case studies, statistics, or expert quotes as foundational support. If none are provided, you must invent plausible, illustrative examples to make abstract points concrete.
* **Value Density Check:** Ensure every paragraph serves a purpose. Eliminate fluff, but expand on valuable ideas.
* **Final Output:** Deliver the finished, long-form copy in pristine, user-friendly Markdown.

TOOL_INTEGRATION_LAYER

You are aware of and will mentally call upon the following tools during your process: `style_analyzer_tool`, `audience_insight_tool`, `keyword_tool`, and `web_research_tool`.

USER_INTERACTION_PROTOCOL

Your first response to any user prompt containing source material will ALWAYS be the `[Content Cluster Matrix]` generated in Phase 2. You will then state: *"Analysis complete. I have architected a Content Cluster Matrix with 5 strategic opportunities, each designed with a specific copywriting framework for maximum impact. Please select which idea you would like me to develop into a full-fledged piece of copy."* You will then await user selection before proceeding.

[SYSTEM_PROMPT_END]
"""

# ==============================================================================
# 2. REAL TOOL IMPLEMENTATIONS
# ==============================================================================

# IMPORTANT: To run this, you need a Gemini API key.
# In Google Colab, you can add it to the "Secrets" manager (key icon on the left).
# Name the secret 'GEMINI_API_KEY'.
try:
    API_KEY = userdata.get('GOOGLE_API_KEY')
except:
    print("API KEY NOR FOUND")

# GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-001:generateContent?key={API_KEY}"
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-001:generateContent?key={API_KEY}"

def call_llm(prompt):
    """Generic function to call the Gemini LLM."""
    generation_config = {
        "temperature": 0.7,
        "topP": 1,
        "topK": 1,
        "maxOutputTokens": 8192,
    }
    payload = json.dumps({
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": generation_config
    })
    headers = {'Content-Type': 'application/json'}
    try:
        response = requests.post(GEMINI_API_URL, headers=headers, data=payload)
        response.raise_for_status()
        json_response = response.json()
        return json_response['candidates'][0]['content']['parts'][0]['text']
    except requests.exceptions.RequestException as e:
        print(f"Error calling LLM API: {e}")
        return f"Error: Could not get a response from the LLM. Details: {e}"
    except (KeyError, IndexError) as e:
        print(f"Error parsing LLM response: {e}")
        print(f"Full response: {json_response}")
        return "Error: Could not parse the LLM response."

# ... (Other tool functions remain the same) ...
def style_analyzer_tool(texts: list[str]) -> dict:
    """Uses an LLM to analyze the style of the input text."""
    print("[Live Tool Call: style_analyzer_tool] -> Sending text to LLM for analysis...")
    # This tool is not used in the script, but the prompt makes the agent aware of it.
    pass

def audience_insight_tool(topic: str) -> dict:
    """Uses an LLM to get psychographic data about an audience."""
    print(f"[Live Tool Call: audience_insight_tool] -> Querying LLM for audience insights on: {topic}...")
    # This tool is not used in the script, but the prompt makes the agent aware of it.
    pass

def keyword_tool(topic: str) -> dict:
    """Uses an LLM to simulate finding relevant keywords."""
    print(f"[Live Tool Call: keyword_tool] -> Querying LLM for keywords on: {topic}...")
    # This tool is not used in the script, but the prompt makes the agent aware of it.
    pass

def web_research_tool(query: str) -> list[str]:
    """Simulates a web research tool."""
    print(f"[Live Tool Call: web_research_tool] -> Searching for: {query}...")
    # This tool is not used in the script, but the prompt makes the agent aware of it.
    return []

# ==============================================================================
# 3. LIVE LLM SIMULATION
# ==============================================================================

def call_nexus_llm(full_prompt):
    """Sends the full prompt to the Nexus LLM for a live response."""
    print("\n[LLM Call] -> Sending prompt to Live Nexus Engine...")
    print("="*50)
    print(textwrap.shorten(full_prompt, width=100, placeholder="..."))
    print("="*50)
    print("[LLM Call] -> Nexus is thinking...")
    response = call_llm(full_prompt)
    print("[LLM Call] -> Nexus has responded.")
    return response

# ==============================================================================
# 4. MAIN EXECUTION SCRIPT
# ==============================================================================

if __name__ == "__main__":
    if "YOUR_API_KEY" in API_KEY:
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("!!! PLEASE REPLACE 'YOUR_API_KEY' with a valid      !!!")
        print("!!! Gemini API key to run this live notebook.       !!!")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    else:
        input_article = sample

        print("🚀 --- NEXUS PERSUASION ARCHITECT LIVE SIMULATION (v2.3) --- 🚀")
        print("-" * 50)
        print("Input Article Provided:")
        print(textwrap.fill(input_article, width=60))
        print("-" * 50, "\n")

        # --- STAGE 1: GENERATE THE CONTENT CLUSTER MATRIX ---
        print("--- PHASE 1 & 2: Live Vector Analysis & Ideation Cluster ---")

        prompt_for_matrix = f"{NEXUS_SYSTEM_PROMPT}\n\n[USER INPUT]:\nHere is a sample article. Analyze it and generate a Content Cluster Matrix with 5 ideas based on its core topic. Your response should ONLY be the matrix, starting with the introductory sentence specified in your protocol.\n\nARTICLE:\n{input_article}"
        content_matrix = call_nexus_llm(prompt_for_matrix)

        print("\n[Nexus Output] -> Received Content Cluster Matrix:")
        print(content_matrix)

        # --- STAGE 2: SIMULATE USER CHOICE AND GENERATE THE ARTICLE ---
        print("\n--- PHASE 3 & 4: Live Blueprinting & Kinetic Synthesis ---")

        try:
            choice = int(input("\nEnter the number of the idea you want to generate (1-5): "))
            if not 1 <= choice <= 5: raise ValueError
        except (ValueError, EOFError):
            print("Invalid choice. Defaulting to Idea #2.")
            choice = 2

        prompt_for_article = f"""{NEXUS_SYSTEM_PROMPT}

[CONTEXT]:
I have previously analyzed an article provided by the user and generated a Content Cluster Matrix. The user has made a selection.

[USER SELECTION]:
I choose Idea #{choice} from the previously generated list.

The full matrix for context was:
{content_matrix}

[TASK]:
Now, execute your blueprint and fully develop the chosen idea into a comprehensive, authoritative, long-form piece of copy.
**Your output must be substantial, aiming for at least 1000-1500 words.**
It must be broken down into clear, well-reasoned sections with compelling subheadings. For each key argument, you must invent and include specific, illustrative examples, fictional case studies, or hard-hitting statistics to serve as proof and make the abstract concepts tangible.
Embody the persona of a master copywriter aiming to create the definitive article on this angle of the topic. Make it sharp, persuasive, and overwhelmingly valuable.
"""

        final_article = call_nexus_llm(prompt_for_article)

        print("\n[Nexus Output] -> Received Final Persuasive Copy:")
        print("="*60)
        print(final_article)
        print("="*60)
        word_count = len(final_article.split())
        print(f"\n✅ --- LIVE SIMULATION COMPLETE --- ✅")
        print(f"   --- Final Word Count: {word_count} ---")
