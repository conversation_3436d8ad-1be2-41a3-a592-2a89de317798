# Intelligent DataForSEO Endpoint Selection System

This document explains how the MCP-based keyword research system intelligently selects the best DataForSEO API endpoints based on task requirements.

## Overview

The system automatically chooses the most appropriate DataForSEO endpoint based on:
- Task type and requirements
- Scale of operation (number of keywords)
- Specific metrics needed
- Performance considerations

## Endpoint Categories

### 1. Keyword Expansion Endpoints

#### Keywords For Keywords (`/v3/keywords_data/google_ads/keywords_for_keywords/live`)
- **Best for**: Small to medium keyword expansion (5-100 keywords)
- **Strengths**: High-quality related keywords, Google Ads data
- **Use case**: When you have 1-5 seed keywords and need 50-100 related terms
- **Selection criteria**: `seed_count <= 5 AND target_count <= 100`

#### Related Keywords (`/v3/dataforseo_labs/google/related_keywords/live`)
- **Best for**: Medium-scale expansion with semantic relationships
- **Strengths**: Semantic clustering, keyword difficulty data
- **Use case**: When you need semantically related keywords with SEO metrics
- **Selection criteria**: `target_count <= 500 AND need_semantic_data = true`

#### Keyword Suggestions (`/v3/dataforseo_labs/google/keyword_suggestions/live`)
- **Best for**: Broad keyword discovery
- **Strengths**: Wide variety of suggestions, search intent data
- **Use case**: Exploratory keyword research for new topics
- **Selection criteria**: `exploration_mode = true`

#### Keyword Ideas (`/v3/dataforseo_labs/google/keyword_ideas/live`)
- **Best for**: Large-scale keyword expansion (500+ keywords)
- **Strengths**: Massive keyword generation, comprehensive data
- **Use case**: Enterprise-level keyword research
- **Selection criteria**: `target_count > 500`

### 2. Keyword Metrics Endpoints

#### Search Volume (`/v3/keywords_data/google_ads/search_volume/live`)
- **Best for**: Volume-only analysis
- **Strengths**: Fast, accurate search volume data
- **Use case**: When you only need search volume metrics
- **Selection criteria**: `volume_only = true`

#### Bulk Keyword Difficulty (`/v3/dataforseo_labs/google/bulk_keyword_difficulty/live`)
- **Best for**: Difficulty analysis for large keyword lists
- **Strengths**: Keyword difficulty scores, ranking probability
- **Use case**: SEO difficulty assessment for content planning
- **Selection criteria**: `need_difficulty = true AND keyword_count > 50`

#### Keyword Overview (`/v3/dataforseo_labs/google/keyword_overview/live`)
- **Best for**: Comprehensive keyword analysis
- **Strengths**: Complete keyword metrics, SERP data
- **Use case**: Detailed analysis of specific keywords
- **Selection criteria**: `need_comprehensive_data = true`

### 3. Competitor Analysis Endpoints

#### Ranked Keywords (`/v3/dataforseo_labs/google/ranked_keywords/live`)
- **Best for**: Domain keyword analysis
- **Strengths**: Actual ranking positions, traffic estimates
- **Use case**: Competitor keyword discovery
- **Selection criteria**: `analysis_type = "ranked_keywords"`

#### Competitors Domain (`/v3/dataforseo_labs/google/competitors_domain/live`)
- **Best for**: Finding competitor domains
- **Strengths**: Competitive landscape mapping
- **Use case**: Market analysis and competitor identification
- **Selection criteria**: `analysis_type = "competitor_discovery"`

### 4. SERP Analysis Endpoints

#### SERP Organic (`/v3/serp/google/organic/live/advanced`)
- **Best for**: Real-time SERP analysis
- **Strengths**: Current SERP features, ranking data
- **Use case**: SERP feature analysis, ranking monitoring
- **Selection criteria**: `analysis_type = "organic"`

## Selection Algorithm

The system uses a rule-based approach to select endpoints:

```python
def select_best_endpoint(task_type: str, requirements: Dict[str, Any]) -> str:
    if task_type == "keyword_expansion":
        seed_count = requirements.get("seed_count", 1)
        target_count = requirements.get("target_count", 100)
        
        if seed_count <= 5 and target_count <= 100:
            return "keywords_for_keywords"
        elif target_count > 500:
            return "keyword_ideas"
        else:
            return "related_keywords"
    
    elif task_type == "keyword_metrics":
        if requirements.get("volume_only", False):
            return "search_volume"
        elif requirements.get("need_difficulty", False):
            return "keyword_difficulty"
        else:
            return "keyword_overview"
```

## Workflow Integration

### Step 1: Website Content Analysis
- **Tool**: `analyze_website_content`
- **Purpose**: Extract business context and generate seed keywords
- **Output**: 10-15 broad seed keywords

### Step 2: Keyword Expansion
- **Tool**: `expand_seed_keywords`
- **Endpoint Selection**: Based on seed count and target keywords
- **Process**:
  1. Analyze requirements (seed count, target count)
  2. Select optimal endpoint
  3. Execute expansion in batches
  4. Deduplicate and rank results

### Step 3: Clustering and Analysis
- **Tool**: `cluster_and_analyze_keywords`
- **Process**:
  1. Semantic clustering by topic
  2. Intent classification
  3. Pillar topic identification
  4. Content strategy development

## Performance Optimization

### Batch Processing
- Process keywords in batches of 5-20
- Avoid API rate limits
- Optimize for cost efficiency

### Caching Strategy
- Cache endpoint responses
- Reuse data across workflow steps
- Minimize redundant API calls

### Error Handling
- Graceful fallback to alternative endpoints
- Retry logic for transient failures
- Comprehensive error reporting

## Cost Optimization

### Endpoint Cost Comparison
1. **Keywords For Keywords**: Medium cost, high quality
2. **Related Keywords**: Higher cost, semantic data
3. **Keyword Ideas**: Highest cost, maximum coverage
4. **Search Volume**: Low cost, basic metrics

### Selection Strategy
- Start with cost-effective endpoints
- Scale up based on requirements
- Balance cost vs. data quality

## Future Enhancements

### Machine Learning Integration
- Train models on endpoint performance
- Predict optimal endpoint selection
- Continuous improvement based on results

### Advanced Clustering
- Use embeddings for semantic clustering
- Industry-specific clustering models
- Intent prediction algorithms

### Real-time Optimization
- Dynamic endpoint switching
- Performance monitoring
- Adaptive selection criteria

## Usage Examples

### Small Business (< 100 keywords)
```python
requirements = {
    "seed_count": 3,
    "target_count": 50,
    "budget": "low"
}
# Selects: keywords_for_keywords
```

### Enterprise (1000+ keywords)
```python
requirements = {
    "seed_count": 10,
    "target_count": 1000,
    "need_comprehensive_data": True
}
# Selects: keyword_ideas
```

### SEO Analysis Focus
```python
requirements = {
    "need_difficulty": True,
    "need_serp_data": True,
    "keyword_count": 200
}
# Selects: keyword_overview
```

## Best Practices

1. **Start Broad**: Begin with general seed keywords
2. **Scale Gradually**: Expand in stages based on results
3. **Monitor Costs**: Track API usage and optimize
4. **Validate Results**: Review keyword relevance regularly
5. **Iterate Strategy**: Refine based on performance data

## Troubleshooting

### Common Issues
- **Rate Limits**: Implement proper delays between requests
- **Invalid Keywords**: Validate input before API calls
- **Empty Results**: Check location and language settings
- **High Costs**: Review endpoint selection criteria

### Solutions
- Use batch processing for efficiency
- Implement caching for repeated requests
- Add fallback endpoints for reliability
- Monitor and optimize selection algorithms
