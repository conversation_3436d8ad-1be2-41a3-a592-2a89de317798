"""
Test script for the Enhanced DataForSEO MCP Client
Tests the expressive API methods and real API integration
"""

import asyncio
import os
from enhanced_dataforseo_mcp_client import get_enhanced_client

async def test_expressive_methods():
    """Test the expressive method names"""
    print("🧪 Testing Enhanced DataForSEO MCP Client")
    print("=" * 50)
    
    client = get_enhanced_client()
    
    # Test 1: Content Parsing
    print("\n1. Testing contentParsing() method...")
    try:
        result = await client.contentParsing("https://example.com")
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: Status {result.get('status_code')}")
            if result.get('tasks'):
                items = result['tasks'][0].get('result', [{}])[0].get('items', [])
                if items:
                    page_content = items[0].get('page_content', {})
                    print(f"   📄 Title: {page_content.get('title', 'N/A')[:50]}...")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Search Intent
    print("\n2. Testing searchIntent() method...")
    try:
        result = await client.searchIntent(["buy shoes", "how to run"])
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: Status {result.get('status_code')}")
            if result.get('tasks'):
                items = result['tasks'][0].get('result', [{}])[0].get('items', [])
                print(f"   🎯 Analyzed {len(items)} keywords for intent")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: SERP Organic
    print("\n3. Testing serpOrganic() method...")
    try:
        result = await client.serpOrganic("digital marketing", location="United States", depth=10)
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: Status {result.get('status_code')}")
            if result.get('tasks'):
                items = result['tasks'][0].get('result', [{}])[0].get('items', [])
                organic_results = [item for item in items if item.get('type') == 'organic']
                print(f"   🔍 Found {len(organic_results)} organic results")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 4: Related Keywords
    print("\n4. Testing relatedKeywords() method...")
    try:
        result = await client.relatedKeywords("seo tools", location="United States", limit=20)
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: Status {result.get('status_code')}")
            if result.get('tasks'):
                items = result['tasks'][0].get('result', [{}])[0].get('items', [])
                print(f"   🔗 Found {len(items)} related keywords")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 5: Keyword Overview
    print("\n5. Testing keywordOverview() method...")
    try:
        result = await client.keywordOverview(["seo", "digital marketing"], location="United States")
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: Status {result.get('status_code')}")
            if result.get('tasks'):
                items = result['tasks'][0].get('result', [{}])[0].get('items', [])
                print(f"   📊 Analyzed {len(items)} keywords for overview")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

async def test_workflow_methods():
    """Test the high-level workflow methods"""
    print("\n" + "=" * 50)
    print("🔄 Testing Workflow Methods")
    print("=" * 50)
    
    client = get_enhanced_client()
    
    # Test 1: Expand Keyword Cluster
    print("\n1. Testing expandKeywordCluster() workflow...")
    try:
        result = await client.expandKeywordCluster("content marketing", location="United States")
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: {result.get('analysis_type')}")
            print(f"   🌱 Seed keyword: {result.get('seed_keyword')}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Analyze Competitor
    print("\n2. Testing analyzeCompetitor() workflow...")
    try:
        result = await client.analyzeCompetitor("example.com", location="United States")
        if "error" in result:
            print(f"   ❌ Error: {result['error']}")
        else:
            print(f"   ✅ Success: {result.get('analysis_type')}")
            print(f"   🏢 Domain: {result.get('domain')}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_method_naming():
    """Test that method names are expressive and readable"""
    print("\n" + "=" * 50)
    print("📝 Testing Method Naming Convention")
    print("=" * 50)
    
    client = get_enhanced_client()
    
    # Check that methods exist and are callable
    expressive_methods = [
        'contentParsing',
        'searchIntent', 
        'serpOrganic',
        'rankedKeywords',
        'categoriesForDomain',
        'keywordIdeas',
        'bulkKeywordDifficulty',
        'relatedKeywords',
        'keywordsForKeywords',
        'keywordOverview',
        'analyzeCompetitor',
        'expandKeywordCluster'
    ]
    
    print("\n✅ Expressive methods available:")
    for method_name in expressive_methods:
        if hasattr(client, method_name):
            print(f"   ✓ {method_name}()")
        else:
            print(f"   ❌ {method_name}() - MISSING")
    
    print("\n🎯 Benefits of expressive naming:")
    print("   • client.contentParsing(url) vs client.call_mcp_endpoint(endpoint, params)")
    print("   • client.serpOrganic(keyword) vs client._call_serp_organic_api(params)")
    print("   • client.searchIntent(keywords) vs client._call_search_intent_api(params)")
    print("   • Much more readable and self-documenting!")

async def main():
    """Run all tests"""
    print("🚀 Enhanced DataForSEO MCP Client Test Suite")
    print("Testing expressive method names and real API integration")
    
    # Check credentials
    if not os.getenv('DATAFORSEO_USERNAME') or not os.getenv('DATAFORSEO_PASSWORD'):
        print("\n⚠️  Warning: DataForSEO credentials not found in environment variables")
        print("   Set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD to test real API calls")
        print("   Tests will show API errors but demonstrate the expressive method structure")
    
    # Test expressive methods
    await test_expressive_methods()
    
    # Test workflow methods
    await test_workflow_methods()
    
    # Test method naming
    test_method_naming()
    
    print("\n" + "=" * 50)
    print("✅ Test Suite Complete!")
    print("The enhanced client provides much more expressive and readable API calls.")

if __name__ == "__main__":
    asyncio.run(main())
