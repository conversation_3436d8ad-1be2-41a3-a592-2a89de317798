"""
Autonomous MCP Agent
An intelligent agent that can autonomously select and call DataForSEO MCP endpoints
based on natural language requests and documentation understanding
"""

import json
import os
import re
from typing import Dict, List, Any, Optional, Tuple
from smolagents import tool
import logging

logger = logging.getLogger(__name__)

# DataForSEO API Documentation Knowledge Base
DATAFORSEO_KNOWLEDGE_BASE = {
    "endpoints": {
        "/v3/keywords_data/google_ads/keywords_for_keywords/live": {
            "description": "Get related keywords for a list of seed keywords using Google Ads data",
            "use_cases": ["keyword expansion", "related keywords", "google ads research"],
            "parameters": ["keywords", "location_name", "language_name"],
            "best_for": "small to medium keyword expansion (5-100 keywords)",
            "cost": "medium",
            "data_quality": "high"
        },
        "/v3/dataforseo_labs/google/related_keywords/live": {
            "description": "Get semantically related keywords with SEO metrics",
            "use_cases": ["semantic keyword research", "seo metrics", "keyword clustering"],
            "parameters": ["keyword", "location_name", "limit"],
            "best_for": "semantic keyword analysis with SEO data",
            "cost": "high",
            "data_quality": "very high"
        },
        "/v3/dataforseo_labs/google/keyword_suggestions/live": {
            "description": "Get keyword suggestions for broad discovery",
            "use_cases": ["keyword discovery", "content ideas", "topic exploration"],
            "parameters": ["keyword", "location_name", "limit"],
            "best_for": "exploratory keyword research",
            "cost": "medium",
            "data_quality": "high"
        },
        "/v3/dataforseo_labs/google/keyword_ideas/live": {
            "description": "Generate large-scale keyword ideas for comprehensive research",
            "use_cases": ["large scale research", "enterprise keywords", "comprehensive analysis"],
            "parameters": ["keyword", "location_name", "limit"],
            "best_for": "enterprise-level keyword research (500+ keywords)",
            "cost": "high",
            "data_quality": "very high"
        },
        "/v3/keywords_data/google_ads/search_volume/live": {
            "description": "Get search volume data for keywords",
            "use_cases": ["search volume", "traffic estimation", "keyword metrics"],
            "parameters": ["keywords", "location_name"],
            "best_for": "volume-only analysis",
            "cost": "low",
            "data_quality": "high"
        },
        "/v3/dataforseo_labs/google/bulk_keyword_difficulty/live": {
            "description": "Get keyword difficulty scores for large keyword lists",
            "use_cases": ["keyword difficulty", "seo difficulty", "ranking probability"],
            "parameters": ["keywords", "location_name"],
            "best_for": "difficulty analysis for large lists",
            "cost": "medium",
            "data_quality": "high"
        },
        "/v3/dataforseo_labs/google/keyword_overview/live": {
            "description": "Get comprehensive keyword analysis with all metrics",
            "use_cases": ["comprehensive analysis", "keyword overview", "detailed metrics"],
            "parameters": ["keywords", "location_name"],
            "best_for": "detailed analysis of specific keywords",
            "cost": "high",
            "data_quality": "very high"
        },
        "/v3/dataforseo_labs/google/ranked_keywords/live": {
            "description": "Get keywords that a domain ranks for",
            "use_cases": ["competitor analysis", "domain keywords", "ranking analysis"],
            "parameters": ["target", "location_name", "limit"],
            "best_for": "competitor keyword discovery",
            "cost": "high",
            "data_quality": "very high"
        },
        "/v3/serp/google/organic/live/advanced": {
            "description": "Get real-time SERP results for keywords",
            "use_cases": ["serp analysis", "ranking monitoring", "serp features"],
            "parameters": ["keyword", "location_name", "device"],
            "best_for": "real-time SERP analysis",
            "cost": "medium",
            "data_quality": "very high"
        }
    },
    "task_patterns": {
        "keyword_expansion": {
            "keywords": ["expand", "related", "similar", "more keywords", "keyword ideas"],
            "preferred_endpoints": [
                "/v3/keywords_data/google_ads/keywords_for_keywords/live",
                "/v3/dataforseo_labs/google/related_keywords/live",
                "/v3/dataforseo_labs/google/keyword_ideas/live"
            ]
        },
        "keyword_metrics": {
            "keywords": ["search volume", "difficulty", "metrics", "cpc", "competition"],
            "preferred_endpoints": [
                "/v3/keywords_data/google_ads/search_volume/live",
                "/v3/dataforseo_labs/google/bulk_keyword_difficulty/live",
                "/v3/dataforseo_labs/google/keyword_overview/live"
            ]
        },
        "competitor_analysis": {
            "keywords": ["competitor", "domain analysis", "ranking keywords", "competitor keywords"],
            "preferred_endpoints": [
                "/v3/dataforseo_labs/google/ranked_keywords/live"
            ]
        },
        "serp_analysis": {
            "keywords": ["serp", "search results", "ranking", "google results"],
            "preferred_endpoints": [
                "/v3/serp/google/organic/live/advanced"
            ]
        }
    }
}

class AutonomousMCPAgent:
    """Autonomous agent that can understand requests and select appropriate MCP endpoints"""
    
    def __init__(self):
        self.knowledge_base = DATAFORSEO_KNOWLEDGE_BASE
    
    def understand_request(self, request: str) -> Dict[str, Any]:
        """
        Analyze a natural language request to understand intent and requirements
        
        Args:
            request: Natural language request
            
        Returns:
            Dict containing parsed intent and requirements
        """
        request_lower = request.lower()
        
        # Extract task type
        task_type = None
        confidence = 0
        
        for task, patterns in self.knowledge_base["task_patterns"].items():
            pattern_matches = sum(1 for keyword in patterns["keywords"] if keyword in request_lower)
            task_confidence = pattern_matches / len(patterns["keywords"])
            
            if task_confidence > confidence:
                confidence = task_confidence
                task_type = task
        
        # Extract parameters
        parameters = {}
        
        # Extract keywords
        keyword_patterns = [
            r'keyword[s]?\s*[:\-]?\s*["\']([^"\']+)["\']',
            r'for\s+["\']([^"\']+)["\']',
            r'analyze\s+["\']([^"\']+)["\']'
        ]
        
        for pattern in keyword_patterns:
            matches = re.findall(pattern, request, re.IGNORECASE)
            if matches:
                parameters["keywords"] = matches
                break
        
        # Extract domain
        domain_pattern = r'(?:domain|website|site)\s*[:\-]?\s*["\']?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})["\']?'
        domain_match = re.search(domain_pattern, request, re.IGNORECASE)
        if domain_match:
            parameters["domain"] = domain_match.group(1)
        
        # Extract location
        location_pattern = r'(?:location|country|region)\s*[:\-]?\s*["\']([^"\']+)["\']'
        location_match = re.search(location_pattern, request, re.IGNORECASE)
        if location_match:
            parameters["location"] = location_match.group(1)
        else:
            parameters["location"] = "United States"  # Default
        
        # Extract scale/count
        count_pattern = r'(\d+)\s*(?:keywords?|results?)'
        count_match = re.search(count_pattern, request, re.IGNORECASE)
        if count_match:
            parameters["target_count"] = int(count_match.group(1))
        
        return {
            "task_type": task_type,
            "confidence": confidence,
            "parameters": parameters,
            "original_request": request
        }
    
    def select_optimal_endpoint(self, intent: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """
        Select the optimal endpoint based on understood intent
        
        Args:
            intent: Parsed intent from understand_request
            
        Returns:
            Tuple of (endpoint_path, endpoint_config)
        """
        task_type = intent.get("task_type")
        parameters = intent.get("parameters", {})
        
        if not task_type:
            # Fallback to keyword overview for unknown tasks
            return "/v3/dataforseo_labs/google/keyword_overview/live", self.knowledge_base["endpoints"]["/v3/dataforseo_labs/google/keyword_overview/live"]
        
        # Get preferred endpoints for task type
        preferred_endpoints = self.knowledge_base["task_patterns"][task_type]["preferred_endpoints"]
        
        # Apply selection logic based on parameters
        if task_type == "keyword_expansion":
            target_count = parameters.get("target_count", 100)
            keyword_count = len(parameters.get("keywords", []))
            
            if keyword_count <= 5 and target_count <= 100:
                endpoint = "/v3/keywords_data/google_ads/keywords_for_keywords/live"
            elif target_count > 500:
                endpoint = "/v3/dataforseo_labs/google/keyword_ideas/live"
            else:
                endpoint = "/v3/dataforseo_labs/google/related_keywords/live"
        
        elif task_type == "keyword_metrics":
            if "volume" in intent["original_request"].lower() and "difficulty" not in intent["original_request"].lower():
                endpoint = "/v3/keywords_data/google_ads/search_volume/live"
            elif "difficulty" in intent["original_request"].lower():
                endpoint = "/v3/dataforseo_labs/google/bulk_keyword_difficulty/live"
            else:
                endpoint = "/v3/dataforseo_labs/google/keyword_overview/live"
        
        else:
            # Use first preferred endpoint
            endpoint = preferred_endpoints[0]
        
        return endpoint, self.knowledge_base["endpoints"][endpoint]
    
    def format_mcp_request(self, endpoint: str, intent: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format the request for the MCP server
        
        Args:
            endpoint: Selected endpoint path
            intent: Parsed intent
            
        Returns:
            Formatted MCP request
        """
        parameters = intent.get("parameters", {})
        
        # Base request structure
        mcp_request = {
            "endpoint": endpoint,
            "parameters": {}
        }
        
        # Map parameters based on endpoint requirements
        if "keywords_for_keywords" in endpoint:
            mcp_request["parameters"] = {
                "keywords": parameters.get("keywords", ["sample keyword"]),
                "location_name": parameters.get("location", "United States")
            }
        
        elif "related_keywords" in endpoint or "keyword_suggestions" in endpoint or "keyword_ideas" in endpoint:
            keywords = parameters.get("keywords", ["sample keyword"])
            mcp_request["parameters"] = {
                "keyword": keywords[0] if keywords else "sample keyword",
                "location_name": parameters.get("location", "United States"),
                "limit": min(parameters.get("target_count", 100), 1000)
            }
        
        elif "search_volume" in endpoint or "bulk_keyword_difficulty" in endpoint or "keyword_overview" in endpoint:
            mcp_request["parameters"] = {
                "keywords": parameters.get("keywords", ["sample keyword"]),
                "location_name": parameters.get("location", "United States")
            }
        
        elif "ranked_keywords" in endpoint:
            mcp_request["parameters"] = {
                "target": parameters.get("domain", "example.com"),
                "location_name": parameters.get("location", "United States"),
                "limit": parameters.get("target_count", 100)
            }
        
        elif "serp" in endpoint:
            keywords = parameters.get("keywords", ["sample keyword"])
            mcp_request["parameters"] = {
                "keyword": keywords[0] if keywords else "sample keyword",
                "location_name": parameters.get("location", "United States"),
                "device": "desktop"
            }
        
        return mcp_request

# Global autonomous agent instance
_autonomous_agent = None

def get_autonomous_agent() -> AutonomousMCPAgent:
    """Get or create the global autonomous agent instance"""
    global _autonomous_agent
    if _autonomous_agent is None:
        _autonomous_agent = AutonomousMCPAgent()
    return _autonomous_agent

@tool
def autonomous_mcp_call(request: str) -> Dict[str, Any]:
    """
    Autonomously understand a request and call the appropriate DataForSEO MCP endpoint
    
    Args:
        request: Natural language request for SEO data
        
    Returns:
        Dict containing the analysis and results
    """
    try:
        agent = get_autonomous_agent()
        
        # Step 1: Understand the request
        intent = agent.understand_request(request)
        
        if intent["confidence"] < 0.3:
            return {
                "error": "Could not understand the request clearly",
                "suggestion": "Please be more specific about what SEO data you need",
                "intent_analysis": intent
            }
        
        # Step 2: Select optimal endpoint
        endpoint, endpoint_config = agent.select_optimal_endpoint(intent)
        
        # Step 3: Format MCP request
        mcp_request = agent.format_mcp_request(endpoint, intent)
        
        # Step 4: Execute the request (simulated for now)
        # In production, this would call the actual MCP server
        result = {
            "status": "success",
            "request_analysis": {
                "original_request": request,
                "understood_task": intent["task_type"],
                "confidence": intent["confidence"],
                "extracted_parameters": intent["parameters"]
            },
            "endpoint_selection": {
                "selected_endpoint": endpoint,
                "selection_reason": endpoint_config["best_for"],
                "cost_estimate": endpoint_config["cost"],
                "data_quality": endpoint_config["data_quality"]
            },
            "mcp_request": mcp_request,
            "simulated_data": {
                "message": f"This would call {endpoint} with parameters: {mcp_request['parameters']}",
                "note": "In production, this would return actual DataForSEO data"
            }
        }
        
        return result
        
    except Exception as e:
        return {"error": f"Autonomous MCP call failed: {str(e)}"}
