"""
Real DataForSEO MCP Client - No Simulation, Real API Calls
Comprehensive implementation using actual DataForSEO APIs through MCP server
"""

import asyncio
import json
import os
import subprocess
import tempfile
from typing import Dict, List, Any, Optional
from smolagents import tool
import logging

logger = logging.getLogger(__name__)


class RealDataForSEOMCPClient:
    """Real MCP client that makes actual DataForSEO API calls"""
    
    def __init__(self, mcp_server_path: Optional[str] = None):
        self.mcp_server_path = mcp_server_path or "dataforseo-mcp-server"
        self.username = os.getenv('DATAFORSEO_USERNAME')
        self.password = os.getenv('DATAFORSEO_PASSWORD')
        
        if not self.username or not self.password:
            logger.warning("DataForSEO credentials not found, will use MCP server defaults")
    
    async def call_dataforseo_endpoint(self, endpoint_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Call DataForSEO endpoint through MCP server"""
        try:
            # Create MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": endpoint_name,
                    "arguments": parameters
                }
            }
            
            # Write request to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(mcp_request, f)
                request_file = f.name
            
            try:
                # Call MCP server
                result = subprocess.run([
                    self.mcp_server_path,
                    "--request-file", request_file
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    response = json.loads(result.stdout)
                    return response.get("result", {})
                else:
                    logger.error(f"MCP server error: {result.stderr}")
                    return {"error": f"MCP server error: {result.stderr}"}
                    
            finally:
                # Clean up temp file
                os.unlink(request_file)
                
        except Exception as e:
            logger.error(f"Error calling DataForSEO endpoint {endpoint_name}: {e}")
            return {"error": str(e)}


# Global client instance
_real_client = None

def get_real_client() -> RealDataForSEOMCPClient:
    """Get or create the global real client instance"""
    global _real_client
    if _real_client is None:
        _real_client = RealDataForSEOMCPClient()
    return _real_client


@tool
def real_serp_based_clustering(
    seed_keywords: List[str],
    location: str = "United States",
    max_clusters: int = 20,
    serp_depth: int = 10
) -> Dict[str, Any]:
    """
    Real SERP-based keyword clustering using actual DataForSEO APIs:
    1. Expand seed keywords using real keyword research endpoints
    2. Get actual SERP results for each keyword
    3. Analyze real ranking pages and their keywords
    4. Build clusters based on actual shared ranking data
    
    Args:
        seed_keywords: Initial seed keywords to expand from
        location: Geographic location for search
        max_clusters: Maximum number of clusters to create
        serp_depth: Number of SERP results to analyze per keyword
    
    Returns:
        Dict containing real clustered keywords with actual ranking analysis
    """
    try:
        client = get_real_client()
        
        # Step 1: Expand seed keywords using real API
        expanded_keywords = []
        for seed in seed_keywords:
            # Use DataForSEO keyword ideas endpoint
            params = {
                "keyword": seed,
                "location_name": location,
                "language_name": "English",
                "limit": 50
            }
            
            result = asyncio.run(client.call_dataforseo_endpoint("keyword_ideas", params))
            
            if "error" not in result and result.get("tasks"):
                items = result.get("tasks", [{}])[0].get("result", [{}])[0].get("items", [])
                for item in items:
                    keyword_data = item.get("keyword_data", {})
                    if keyword_data.get("keyword"):
                        expanded_keywords.append({
                            "keyword": keyword_data["keyword"],
                            "search_volume": keyword_data.get("keyword_info", {}).get("search_volume", 0),
                            "difficulty": keyword_data.get("keyword_properties", {}).get("keyword_difficulty", 0),
                            "cpc": keyword_data.get("keyword_info", {}).get("cpc", 0)
                        })
        
        # Step 2: Get real SERP results for each keyword
        keyword_serp_mapping = {}
        page_keyword_mapping = {}
        
        for kw_data in expanded_keywords[:50]:  # Limit for API costs
            keyword = kw_data["keyword"]
            
            # Get real SERP results
            serp_params = {
                "keyword": keyword,
                "location_name": location,
                "language_name": "English",
                "depth": serp_depth
            }
            
            serp_result = asyncio.run(client.call_dataforseo_endpoint("serp_organic", serp_params))
            
            if "error" not in serp_result and serp_result.get("tasks"):
                serp_items = serp_result.get("tasks", [{}])[0].get("result", [{}])[0].get("items", [])
                ranking_pages = []
                
                for item in serp_items[:serp_depth]:
                    if item.get("type") == "organic":
                        page_url = item.get("url", "")
                        domain = item.get("domain", "")
                        rank = item.get("rank_absolute", 999)
                        
                        page_info = {
                            "url": page_url,
                            "domain": domain,
                            "rank": rank,
                            "title": item.get("title", "")
                        }
                        ranking_pages.append(page_info)
                        
                        # Track which keywords each page ranks for
                        if page_url not in page_keyword_mapping:
                            page_keyword_mapping[page_url] = []
                        page_keyword_mapping[page_url].append({
                            "keyword": keyword,
                            "rank": rank,
                            "search_volume": kw_data["search_volume"],
                            "difficulty": kw_data["difficulty"]
                        })
                
                keyword_serp_mapping[keyword] = ranking_pages
        
        # Step 3: For each ranking page, get real additional keywords using ranked_keywords endpoint
        enhanced_page_keywords = {}
        
        for page_url, keywords in page_keyword_mapping.items():
            if len(keywords) >= 2:  # Only analyze pages ranking for multiple keywords
                domain = keywords[0].get("domain", page_url.split("/")[2] if "/" in page_url else page_url)
                
                # Get real ranked keywords for this domain
                ranked_params = {
                    "target": domain,
                    "location_name": location,
                    "language_name": "English",
                    "limit": 100
                }
                
                ranked_result = asyncio.run(client.call_dataforseo_endpoint("ranked_keywords", ranked_params))
                
                if "error" not in ranked_result and ranked_result.get("tasks"):
                    ranked_items = ranked_result.get("tasks", [{}])[0].get("result", [{}])[0].get("items", [])
                    
                    additional_keywords = []
                    for item in ranked_items:
                        kw_data = item.get("keyword_data", {})
                        rank_data = item.get("ranked_serp_element", {}).get("serp_item", {})
                        
                        if kw_data.get("keyword"):
                            additional_keywords.append({
                                "keyword": kw_data["keyword"],
                                "rank": rank_data.get("rank_absolute", 999),
                                "search_volume": kw_data.get("keyword_info", {}).get("search_volume", 0),
                                "difficulty": kw_data.get("keyword_properties", {}).get("keyword_difficulty", 0)
                            })
                    
                    enhanced_page_keywords[page_url] = {
                        "original_keywords": keywords,
                        "additional_keywords": additional_keywords[:50],  # Limit for performance
                        "domain": domain
                    }
        
        # Step 4: Build real clusters based on actual shared ranking pages
        clusters = []
        processed_keywords = set()
        
        for page_url, page_data in enhanced_page_keywords.items():
            if len(clusters) >= max_clusters:
                break
                
            all_keywords = page_data["original_keywords"] + page_data["additional_keywords"]
            cluster_keywords = []
            
            for kw_data in all_keywords:
                keyword = kw_data["keyword"]
                if keyword not in processed_keywords and kw_data["search_volume"] > 100:
                    cluster_keywords.append(kw_data)
                    processed_keywords.add(keyword)
            
            if len(cluster_keywords) >= 3:  # Minimum cluster size
                # Determine cluster theme based on actual keywords
                cluster_theme = _determine_real_cluster_theme(cluster_keywords)
                
                # Calculate real cluster metrics
                total_volume = sum(kw["search_volume"] for kw in cluster_keywords)
                avg_difficulty = sum(kw["difficulty"] for kw in cluster_keywords) / len(cluster_keywords)
                
                clusters.append({
                    "cluster_id": len(clusters) + 1,
                    "theme": cluster_theme,
                    "anchor_page": {
                        "url": page_url,
                        "domain": page_data["domain"]
                    },
                    "keywords": cluster_keywords,
                    "metrics": {
                        "total_search_volume": total_volume,
                        "average_difficulty": round(avg_difficulty, 2),
                        "keyword_count": len(cluster_keywords),
                        "estimated_traffic_potential": _calculate_real_traffic_potential(cluster_keywords)
                    }
                })
        
        return {
            "status": "success",
            "methodology": "Real SERP-based clustering with actual DataForSEO API data",
            "clusters": clusters,
            "summary": {
                "total_clusters": len(clusters),
                "total_keywords_processed": len(processed_keywords),
                "total_pages_analyzed": len(enhanced_page_keywords),
                "seed_keywords": seed_keywords,
                "api_calls_made": len(seed_keywords) + len(expanded_keywords) + len(page_keyword_mapping)
            },
            "data_sources": [
                "DataForSEO Keyword Ideas API",
                "DataForSEO SERP Organic API", 
                "DataForSEO Ranked Keywords API"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in real SERP-based clustering: {e}")
        return {"error": str(e)}


def _determine_real_cluster_theme(keywords: List[Dict[str, Any]]) -> str:
    """Determine cluster theme based on real keyword analysis"""
    keyword_texts = [kw["keyword"].lower() for kw in keywords]
    all_text = " ".join(keyword_texts)
    
    # Enhanced theme patterns based on real SEO data
    themes = {
        "software_tools": ["software", "tool", "app", "platform", "solution", "system"],
        "how_to_guides": ["how to", "guide", "tutorial", "step", "learn", "instructions"],
        "comparisons": ["best", "top", "review", "compare", "vs", "versus", "alternative"],
        "services": ["service", "company", "agency", "consultant", "expert", "professional"],
        "pricing": ["price", "cost", "cheap", "expensive", "budget", "free", "plan"],
        "features": ["feature", "benefit", "advantage", "capability", "function"],
        "problems": ["problem", "issue", "error", "fix", "solve", "troubleshoot", "help"]
    }
    
    theme_scores = {}
    for theme, terms in themes.items():
        score = sum(1 for term in terms if term in all_text)
        if score > 0:
            theme_scores[theme] = score
    
    if theme_scores:
        best_theme = max(theme_scores.keys(), key=lambda x: theme_scores[x])
        return best_theme
    
    # Fallback: use most common meaningful word
    words = all_text.split()
    word_freq = {}
    stop_words = {"the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an"}
    
    for word in words:
        if len(word) > 3 and word not in stop_words:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    if word_freq:
        most_common = max(word_freq.keys(), key=lambda x: word_freq[x])
        return f"'{most_common}' related"
    
    return "general"


def _calculate_real_traffic_potential(keywords: List[Dict[str, Any]]) -> float:
    """Calculate real traffic potential based on actual search volumes and difficulty"""
    total_potential = 0
    
    for kw in keywords:
        search_volume = kw.get("search_volume", 0)
        difficulty = kw.get("difficulty", 100)
        
        # Estimate CTR based on difficulty (easier keywords = higher potential CTR)
        estimated_ctr = max(0.01, (100 - difficulty) / 100 * 0.3)  # Max 30% CTR for easy keywords
        
        # Calculate potential traffic
        potential_traffic = search_volume * estimated_ctr
        total_potential += potential_traffic
    
    return round(total_potential, 2)


@tool
def real_website_content_analysis(
    website_url: str,
    max_pages: int = 5,
    include_technical_analysis: bool = True
) -> Dict[str, Any]:
    """
    Real website content analysis using actual DataForSEO OnPage API

    Args:
        website_url: Website URL to analyze
        max_pages: Maximum number of pages to crawl and analyze
        include_technical_analysis: Whether to include technical SEO analysis

    Returns:
        Dict containing real website analysis data
    """
    try:
        client = get_real_client()

        # Step 1: Start OnPage crawl task
        crawl_params = {
            "target": website_url,
            "max_crawl_pages": max_pages,
            "load_resources": True,
            "enable_javascript": True,
            "custom_js": "meta",
            "browser_preset": "desktop"
        }

        # Post crawl task
        task_result = asyncio.run(client.call_dataforseo_endpoint("onpage_task_post", crawl_params))

        if "error" in task_result:
            return {"error": f"Failed to start crawl task: {task_result['error']}"}

        # Get task ID
        task_id = None
        if task_result.get("tasks"):
            task_id = task_result["tasks"][0].get("id")

        if not task_id:
            return {"error": "No task ID returned from crawl request"}

        # Wait for task completion (simplified - in real implementation would poll)
        await asyncio.sleep(30)  # Wait for crawl to complete

        # Step 2: Get crawl summary
        summary_result = asyncio.run(client.call_dataforseo_endpoint("onpage_summary", {"id": task_id}))

        # Step 3: Get page details
        pages_result = asyncio.run(client.call_dataforseo_endpoint("onpage_pages", {"id": task_id}))

        # Step 4: Get content parsing for main pages
        content_analysis = {}
        if pages_result.get("tasks"):
            pages = pages_result["tasks"][0].get("result", [{}])[0].get("items", [])

            for page in pages[:3]:  # Analyze top 3 pages
                page_url = page.get("url", "")
                if page_url:
                    content_params = {"url": page_url}
                    content_result = asyncio.run(client.call_dataforseo_endpoint("content_parsing", content_params))

                    if content_result.get("tasks"):
                        content_data = content_result["tasks"][0].get("result", [{}])[0].get("items", [])
                        if content_data:
                            content_analysis[page_url] = content_data[0]

        # Step 5: Extract business context from real content
        business_analysis = _analyze_real_business_context(content_analysis, summary_result)

        # Step 6: Generate real seed keywords from actual content
        seed_keywords = _extract_real_seed_keywords(content_analysis)

        # Step 7: Technical analysis from real crawl data
        technical_analysis = {}
        if include_technical_analysis and summary_result.get("tasks"):
            summary_data = summary_result["tasks"][0].get("result", [{}])[0]
            technical_analysis = _analyze_real_technical_seo(summary_data, pages_result)

        return {
            "status": "success",
            "website_url": website_url,
            "crawl_task_id": task_id,
            "pages_analyzed": len(content_analysis),
            "business_analysis": business_analysis,
            "content_analysis": content_analysis,
            "technical_analysis": technical_analysis,
            "seed_keywords": seed_keywords,
            "recommendations": _generate_real_recommendations(business_analysis, technical_analysis, seed_keywords),
            "data_sources": [
                "DataForSEO OnPage API",
                "DataForSEO Content Parsing API"
            ]
        }

    except Exception as e:
        logger.error(f"Error in real website content analysis: {e}")
        return {"error": str(e)}


def _analyze_real_business_context(content_analysis: Dict[str, Any], summary_result: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze business context from real crawled content"""
    all_content = ""
    page_titles = []
    meta_descriptions = []

    # Extract real content from crawled pages
    for page_url, content_data in content_analysis.items():
        page_content = content_data.get("page_content", {})

        title = page_content.get("title", "")
        description = page_content.get("description", "")
        text_content = page_content.get("text_content", "")

        if title:
            page_titles.append(title)
        if description:
            meta_descriptions.append(description)
        if text_content:
            all_content += f" {text_content}"

    combined_text = f"{' '.join(page_titles)} {' '.join(meta_descriptions)} {all_content}".lower()

    # Real business type detection from actual content
    business_types = {
        "saas": ["software", "platform", "tool", "app", "solution", "dashboard", "api", "cloud"],
        "ecommerce": ["shop", "store", "buy", "product", "cart", "checkout", "order", "shipping"],
        "consulting": ["consulting", "consultant", "advisory", "expert", "professional", "strategy"],
        "agency": ["agency", "marketing", "design", "development", "services", "creative"],
        "education": ["course", "training", "learn", "education", "tutorial", "certification"],
        "healthcare": ["health", "medical", "doctor", "clinic", "treatment", "wellness"],
        "finance": ["finance", "investment", "banking", "loan", "insurance", "financial"],
        "real_estate": ["real estate", "property", "housing", "rental", "mortgage", "homes"]
    }

    detected_type = "general"
    max_score = 0

    for biz_type, keywords in business_types.items():
        score = sum(1 for keyword in keywords if keyword in combined_text)
        if score > max_score:
            max_score = score
            detected_type = biz_type

    # Detect target audience from real content patterns
    b2b_indicators = ["enterprise", "business", "company", "organization", "professional", "corporate"]
    b2c_indicators = ["personal", "individual", "family", "home", "lifestyle", "consumer"]

    b2b_score = sum(1 for indicator in b2b_indicators if indicator in combined_text)
    b2c_score = sum(1 for indicator in b2c_indicators if indicator in combined_text)

    target_audience = "b2b" if b2b_score > b2c_score else "b2c" if b2c_score > 0 else "general"

    return {
        "business_type": detected_type,
        "confidence_score": min(max_score / 5, 1.0),  # Normalize confidence
        "target_audience": target_audience,
        "content_themes": _extract_real_content_themes(combined_text),
        "page_count_analyzed": len(content_analysis),
        "total_content_length": len(all_content)
    }


def _extract_real_seed_keywords(content_analysis: Dict[str, Any]) -> List[str]:
    """Extract seed keywords from real content analysis"""
    seed_keywords = []

    # Extract from real keyword density data
    for page_url, content_data in content_analysis.items():
        page_content = content_data.get("page_content", {})
        keywords_density = page_content.get("keywords_density", [])

        # Get top keywords by density
        for kw_data in keywords_density[:5]:  # Top 5 per page
            keyword = kw_data.get("keyword", "")
            density = kw_data.get("density", 0)

            if keyword and density > 0.5 and len(keyword.split()) <= 3:  # Meaningful keywords only
                if keyword not in seed_keywords:
                    seed_keywords.append(keyword)

    # Extract from titles and meta descriptions
    for page_url, content_data in content_analysis.items():
        page_content = content_data.get("page_content", {})
        title = page_content.get("title", "")
        description = page_content.get("description", "")

        # Extract 2-3 word phrases from titles and descriptions
        import re
        text = f"{title} {description}".lower()
        words = re.findall(r'\b\w+\b', text)

        # Filter out stop words
        stop_words = {"the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an", "is", "are"}
        filtered_words = [w for w in words if w not in stop_words and len(w) > 2]

        # Generate 2-word combinations
        for i in range(len(filtered_words) - 1):
            phrase = f"{filtered_words[i]} {filtered_words[i+1]}"
            if phrase not in seed_keywords and len(seed_keywords) < 20:
                seed_keywords.append(phrase)

    return seed_keywords[:15]  # Return top 15


def _analyze_real_technical_seo(summary_data: Dict[str, Any], pages_result: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze technical SEO from real crawl data"""
    technical_issues = {
        "critical_issues": [],
        "warnings": [],
        "recommendations": []
    }

    # Extract real technical data from summary
    crawl_progress = summary_data.get("crawl_progress", {})
    pages_in_queue = crawl_progress.get("pages_in_queue", 0)
    pages_crawled = crawl_progress.get("pages_crawled", 0)

    # Analyze pages for technical issues
    if pages_result.get("tasks"):
        pages = pages_result["tasks"][0].get("result", [{}])[0].get("items", [])

        status_codes = {}
        page_sizes = []
        load_times = []

        for page in pages:
            # Status code analysis
            status_code = page.get("status_code", 200)
            status_codes[status_code] = status_codes.get(status_code, 0) + 1

            # Page size analysis
            page_size = page.get("size", 0)
            if page_size > 0:
                page_sizes.append(page_size)

            # Load time analysis
            load_time = page.get("load_time", 0)
            if load_time > 0:
                load_times.append(load_time)

            # Check for specific issues
            if status_code >= 400:
                technical_issues["critical_issues"].append(f"Page {page.get('url')} returns {status_code}")

            if page_size > 3000000:  # 3MB
                technical_issues["warnings"].append(f"Large page size: {page.get('url')} ({page_size} bytes)")

            if load_time > 5000:  # 5 seconds
                technical_issues["warnings"].append(f"Slow loading page: {page.get('url')} ({load_time}ms)")

    # Calculate averages
    avg_page_size = sum(page_sizes) / len(page_sizes) if page_sizes else 0
    avg_load_time = sum(load_times) / len(load_times) if load_times else 0

    return {
        "crawl_summary": {
            "pages_crawled": pages_crawled,
            "pages_in_queue": pages_in_queue,
            "status_code_distribution": status_codes
        },
        "performance_metrics": {
            "average_page_size": round(avg_page_size, 2),
            "average_load_time": round(avg_load_time, 2),
            "total_pages_analyzed": len(pages) if 'pages' in locals() else 0
        },
        "issues": technical_issues,
        "health_score": _calculate_technical_health_score(technical_issues, status_codes)
    }


def _extract_real_content_themes(content_text: str) -> List[str]:
    """Extract content themes from real content"""
    themes = []

    # Define theme keywords
    theme_patterns = {
        "technology": ["tech", "software", "digital", "ai", "automation", "cloud", "data"],
        "marketing": ["marketing", "advertising", "seo", "social", "content", "brand"],
        "business": ["business", "strategy", "growth", "revenue", "profit", "sales"],
        "education": ["learn", "training", "course", "education", "skill", "knowledge"],
        "health": ["health", "wellness", "fitness", "medical", "care", "treatment"],
        "finance": ["finance", "money", "investment", "banking", "payment", "cost"]
    }

    for theme, keywords in theme_patterns.items():
        score = sum(1 for keyword in keywords if keyword in content_text)
        if score >= 2:  # Threshold for theme presence
            themes.append(theme)

    return themes


def _calculate_technical_health_score(issues: Dict[str, List], status_codes: Dict[int, int]) -> int:
    """Calculate technical health score from real issues"""
    score = 100

    # Deduct for critical issues
    score -= len(issues.get("critical_issues", [])) * 10

    # Deduct for warnings
    score -= len(issues.get("warnings", [])) * 5

    # Deduct for error status codes
    total_pages = sum(status_codes.values())
    if total_pages > 0:
        error_pages = sum(count for status, count in status_codes.items() if status >= 400)
        error_ratio = error_pages / total_pages
        score -= error_ratio * 30

    return max(0, min(100, score))


def _generate_real_recommendations(business_analysis: Dict[str, Any], technical_analysis: Dict[str, Any], seed_keywords: List[str]) -> List[str]:
    """Generate recommendations based on real analysis data"""
    recommendations = []

    # Business-based recommendations
    business_type = business_analysis.get("business_type", "general")
    confidence = business_analysis.get("confidence_score", 0)

    if confidence < 0.5:
        recommendations.append("Clarify business messaging and value proposition on key pages")

    if business_type == "saas":
        recommendations.append("Focus on feature-based and comparison keywords for SaaS positioning")
    elif business_type == "ecommerce":
        recommendations.append("Optimize for product and transactional keywords")

    # Technical recommendations
    health_score = technical_analysis.get("health_score", 100)
    if health_score < 80:
        recommendations.append(f"Address technical issues (current health score: {health_score}/100)")

    critical_issues = len(technical_analysis.get("issues", {}).get("critical_issues", []))
    if critical_issues > 0:
        recommendations.append(f"Fix {critical_issues} critical technical issues immediately")

    # Content recommendations
    if len(seed_keywords) < 10:
        recommendations.append("Expand content to target more relevant keywords")

    recommendations.append(f"Develop content strategy around {len(seed_keywords)} identified seed keywords")

    return recommendations
