from typing import List, Dict, Any
from smolagents.memory import Agent<PERSON><PERSON>ory, MemoryStep
from smolagents.models import ChatMessage

try:
    import weaviate
except ImportError:
    weaviate = None

try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    SentenceTransformer = None

# Initialize embedding model (you can replace with your preferred model)
embedding_model = None
if SentenceTransformer:
    embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

class WeaviateAgentMemoryStore:
    def __init__(self, weaviate_url: str = "http://localhost:8080"):
        if weaviate is None:
            raise ImportError("weaviate package is not installed.")
        if embedding_model is None:
            raise ImportError("sentence_transformers package is not installed.")
        self.client = weaviate.Client(weaviate_url)
        self._ensure_schema()

    def _ensure_schema(self):
        schema = {
            "classes": [
                {
                    "class": "AgentMemoryStep",
                    "description": "A step in the agent memory",
                    "properties": [
                        {
                            "name": "stepType",
                            "dataType": ["string"],
                            "description": "Type of the memory step"
                        },
                        {
                            "name": "content",
                            "dataType": ["text"],
                            "description": "Text content of the step"
                        },
                        {
                            "name": "metadata",
                            "dataType": ["text"],
                            "description": "Additional metadata as JSON string"
                        },
                        {
                            "name": "contextPath",
                            "dataType": ["string[]"],
                            "description": "Hierarchical context path representing topology of contexts"
                        }
                    ]
                }
            ]
        }
        if not self.client.schema.contains({"classes": [{"class": "AgentMemoryStep"}]}):
            self.client.schema.create(schema)

    def _embed_text(self, text: str) -> List[float]:
        if embedding_model is None:
            raise RuntimeError("Embedding model is not initialized.")
        return embedding_model.encode(text).tolist()

    def persist_agent_memory(self, agent_memory: AgentMemory, metadata: Dict[str, Any] = None, context_path: List[str] = None):
        """
        Persist all steps of an AgentMemory instance into Weaviate.
        context_path: list of strings representing hierarchical context topology path
        """
        for step in agent_memory.steps:
            content = self._extract_step_content(step)
            if not content:
                continue
            vector = self._embed_text(content)
            properties = {
                "stepType": type(step).__name__,
                "content": content,
                "metadata": str(metadata) if metadata is not None else "{}",
                "contextPath": context_path if context_path is not None else []
            }
            self.client.data_object.create(
                data_object=properties,
                class_name="AgentMemoryStep",
                vector=vector
            )

    def _extract_step_content(self, step: MemoryStep) -> str:
        """
        Extract meaningful text content from a memory step.
        """
        # Use getattr with default None to avoid attribute errors
        plan = getattr(step, "plan", None)
        if plan:
            return plan
        task = getattr(step, "task", None)
        if task:
            return task
        model_output = getattr(step, "model_output", None)
        if model_output:
            return model_output
        system_prompt = getattr(step, "system_prompt", None)
        if system_prompt:
            return system_prompt
        # Fallback: try to get string representation
        return str(step)

    def search_relevant_context(self, query: str, limit: int = 5, context_path_filter: List[str] = None) -> List[Dict[str, Any]]:
        """
        Search for relevant agent memory steps based on a query string.
        Optionally filter by context_path_filter to restrict search to specific context topology branches.
        """
        vector = self._embed_text(query)
        query_builder = (
            self.client.query
            .get("AgentMemoryStep", ["content", "stepType", "metadata", "contextPath"])
            .with_near_vector({"vector": vector})
            .with_limit(limit)
        )
        if context_path_filter is not None:
            # Filter by contextPath containing all elements in context_path_filter
            # Weaviate supports filters, but for simplicity, we use a where filter with operator 'ContainsAll'
            query_builder = query_builder.with_where({
                "path": ["contextPath"],
                "operator": "ContainsAll",
                "valueStringArray": context_path_filter
            })
        result = query_builder.do()
        return result.get("data", {}).get("Get", {}).get("AgentMemoryStep", [])

    def fetch_and_stitch_context(self, agent_memory: AgentMemory, query: str, limit: int = 5, context_path_filter: List[str] = None):
        """
        Fetch relevant context from Weaviate and stitch into the given AgentMemory instance.
        Optionally filter by context_path_filter to restrict search to specific context topology branches.
        """
        relevant_steps = self.search_relevant_context(query, limit, context_path_filter)
        for item in relevant_steps:
            content = item.get("content", "")
            # Add as a PlanningStep to agent memory
            from smolagents.memory import PlanningStep
            planning_step = PlanningStep(
                model_input_messages=[],
                model_output_message=ChatMessage(role="assistant", content=content),
                plan=content
            )
            agent_memory.steps.append(planning_step)


# Example usage
if __name__ == "__main__":
    # Initialize store
    try:
        store = WeaviateAgentMemoryStore("http://localhost:8080")
    except ImportError as e:
        print(f"Import error: {e}")
        exit(1)

    # Create a dummy AgentMemory instance
    from smolagents.memory import AgentMemory
    agent_memory = AgentMemory(system_prompt="You are an assistant.")

    # Add some steps
    from smolagents.memory import TaskStep
    agent_memory.steps.append(TaskStep(task="Research sustainable gardening."))

    # Persist agent memory
    store.persist_agent_memory(agent_memory, metadata={"session": "session1"})

    # Search and stitch context
    query = "gardening tips for urban areas"
    store.fetch_and_stitch_context(agent_memory, query)

    # Print stitched steps
    for step in agent_memory.steps:
        content = getattr(step, 'plan', None) or getattr(step, 'task', None) or ''
        print(f"Step type: {type(step).__name__}, Content: {content}")
