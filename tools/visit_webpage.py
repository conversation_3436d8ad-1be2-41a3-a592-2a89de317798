from typing import Any, Optional
from smolagents.tools import Tool
import requests
import markdownify
import smolagents

class VisitWebpageTool(Tool):
    name = "visit_webpage"
    description = "Visits a webpage at the given url and reads its content as a markdown string. Use this to browse webpages."
    inputs = {'url': {'type': 'string', 'description': 'The url of the webpage to visit.'}}
    output_type = "string"

    def forward(self, url: str) -> str:
        try:
            import requests
            from markdownify import markdownify
            from requests.exceptions import RequestException

            from smolagents.utils import truncate_content
        except ImportError as e:
            raise ImportError(
                "You must install packages `markdownify` and `requests` to run this tool: for instance run `pip install markdownify requests`."
            ) from e
        try:
            # Send a GET request to the URL with a 20-second timeout
            response = requests.get(url, timeout=20)
            response.raise_for_status()  # Raise an exception for bad status codes

            # Convert the HTML content to Markdown
            markdown_content = markdownify(response.text).strip()

            # Remove multiple line breaks
            markdown_content = re.sub(r"\n{3,}", "\n\n", markdown_content)

            return truncate_content(markdown_content, 10000)

        except requests.exceptions.Timeout:
            return "The request timed out. Please try again later or check the URL."
        except RequestException as e:
            return f"Error fetching the webpage: {str(e)}"
        except Exception as e:
            return f"An unexpected error occurred: {str(e)}"

    def __init__(self, *args, **kwargs):
        self.is_initialized = False


jina_ai_key = 'jina_d8e8cda611b240f5951c5bb284df8f92UdZ9NMQ5k8WJcTp5my4qtjBnU_xD'
class VisitWebpageMD(Tool):
    name = "visit_web_page_md"
    description = "Visits a webpage at the given url and reads its content as a markdown document via Jina. Use this to browse webpages. It skips images / medias"
    inputs = {'url': {'type': 'string', 'description': 'The url of the webpage to visit.'}}
    
    output_type = "string"

    def forward(self, url: str) -> str:
        import requests
        
        try:
            response = requests.get('https://r.jina.ai/' +url)
            response.raise_for_status()  # Raise an exception for bad status codes
            return response.text.strip()
        
        except Exception as e:
            return f"An unexpected error occurred: {str(e)}"
    
    def __init__(self, *args, **kwargs):
        self.is_initialized = False
        
    