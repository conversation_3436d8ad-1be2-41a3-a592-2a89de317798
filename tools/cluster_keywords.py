"""
Keyword Clustering Tool
Groups related keywords into clusters based on topic and intent
"""

from smolagents import tool
from typing import List, Dict, Any
import json
from collections import defaultdict
import re

@tool
def cluster_keywords(keywords: List[Dict]) -> Dict[str, Any]:
    """
    Groups keywords into clusters based on semantic similarity and search intent.
    
    Args:
        keywords (List[Dict]): List of keyword objects with 'keyword' field and metrics
        
    Returns:
        Dict containing:
        - clusters: List of keyword clusters with metadata
        - cluster_summary: Summary statistics
        - intent_distribution: Breakdown by search intent
    """
    
    def determine_search_intent(keyword: str) -> str:
        """Determine search intent based on keyword patterns"""
        keyword_lower = keyword.lower()
        
        # Transactional intent patterns
        transactional_patterns = [
            'buy', 'purchase', 'order', 'price', 'cost', 'cheap', 'discount',
            'deal', 'sale', 'shop', 'store', 'online', 'free shipping'
        ]
        
        # Commercial intent patterns
        commercial_patterns = [
            'best', 'top', 'review', 'comparison', 'vs', 'alternative',
            'recommendation', 'rating', 'compare', 'choose'
        ]
        
        # Informational intent patterns
        informational_patterns = [
            'what is', 'how to', 'why', 'when', 'where', 'guide', 'tutorial',
            'tips', 'learn', 'understand', 'explain', 'definition'
        ]
        
        # Navigational intent patterns
        navigational_patterns = [
            'login', 'sign in', 'account', 'dashboard', 'portal', 'official',
            'website', 'homepage', 'contact'
        ]
        
        # Check patterns in order of specificity
        if any(pattern in keyword_lower for pattern in transactional_patterns):
            return 'transactional'
        elif any(pattern in keyword_lower for pattern in commercial_patterns):
            return 'commercial'
        elif any(pattern in keyword_lower for pattern in navigational_patterns):
            return 'navigational'
        elif any(pattern in keyword_lower for pattern in informational_patterns):
            return 'informational'
        else:
            # Default classification based on keyword structure
            if len(keyword.split()) > 3:
                return 'informational'  # Longer queries tend to be informational
            else:
                return 'commercial'     # Shorter queries tend to be commercial
    
    def extract_topic_keywords(keyword: str) -> List[str]:
        """Extract main topic words from a keyword"""
        # Remove common stop words and modifiers
        stop_words = {
            'best', 'top', 'how', 'to', 'what', 'is', 'the', 'a', 'an', 'and',
            'or', 'but', 'in', 'on', 'at', 'for', 'with', 'by', 'from', 'of'
        }
        
        words = re.findall(r'\b\w+\b', keyword.lower())
        topic_words = [word for word in words if word not in stop_words and len(word) > 2]
        return topic_words
    
    def calculate_similarity(keyword1: str, keyword2: str) -> float:
        """Calculate similarity between two keywords based on common words"""
        words1 = set(extract_topic_keywords(keyword1))
        words2 = set(extract_topic_keywords(keyword2))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def find_cluster_theme(cluster_keywords: List[str]) -> str:
        """Determine the main theme of a keyword cluster"""
        # Count word frequency across all keywords in cluster
        word_count = defaultdict(int)
        
        for keyword in cluster_keywords:
            topic_words = extract_topic_keywords(keyword)
            for word in topic_words:
                word_count[word] += 1
        
        # Find most common words
        if word_count:
            most_common = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
            # Use top 2-3 words to create theme
            theme_words = [word for word, count in most_common[:3] if count > 1]
            return ' '.join(theme_words) if theme_words else 'general'
        
        return 'general'
    
    try:
        if not keywords:
            return {
                'clusters': [],
                'cluster_summary': {'total_clusters': 0, 'total_keywords': 0},
                'intent_distribution': {}
            }
        
        # Add search intent to each keyword
        enriched_keywords = []
        for kw in keywords:
            kw_copy = kw.copy()
            kw_copy['search_intent'] = determine_search_intent(kw['keyword'])
            enriched_keywords.append(kw_copy)
        
        # Group keywords by similarity
        clusters = []
        processed = set()
        similarity_threshold = 0.3
        
        for i, keyword_data in enumerate(enriched_keywords):
            if i in processed:
                continue
            
            # Start new cluster
            cluster = [keyword_data]
            processed.add(i)
            
            # Find similar keywords
            for j, other_keyword_data in enumerate(enriched_keywords):
                if j in processed or i == j:
                    continue
                
                similarity = calculate_similarity(
                    keyword_data['keyword'], 
                    other_keyword_data['keyword']
                )
                
                if similarity >= similarity_threshold:
                    cluster.append(other_keyword_data)
                    processed.add(j)
            
            clusters.append(cluster)
        
        # Process clusters and add metadata
        processed_clusters = []
        for i, cluster in enumerate(clusters):
            cluster_keywords = [kw['keyword'] for kw in cluster]
            theme = find_cluster_theme(cluster_keywords)
            
            # Calculate cluster metrics
            total_frequency = sum(kw.get('frequency', 0) for kw in cluster)
            avg_content_score = sum(kw.get('content_score', 0) for kw in cluster) / len(cluster)
            
            # Determine dominant intent
            intent_counts = defaultdict(int)
            for kw in cluster:
                intent_counts[kw['search_intent']] += 1
            dominant_intent = max(intent_counts.items(), key=lambda x: x[1])[0]
            
            # Identify primary and supporting keywords
            cluster_sorted = sorted(cluster, key=lambda x: x.get('content_score', 0), reverse=True)
            primary_keywords = cluster_sorted[:3]
            supporting_keywords = cluster_sorted[3:]
            
            processed_cluster = {
                'cluster_id': i + 1,
                'theme': theme,
                'dominant_intent': dominant_intent,
                'keyword_count': len(cluster),
                'primary_keywords': [kw['keyword'] for kw in primary_keywords],
                'supporting_keywords': [kw['keyword'] for kw in supporting_keywords],
                'all_keywords': cluster,
                'metrics': {
                    'total_frequency': total_frequency,
                    'avg_content_score': round(avg_content_score, 2),
                    'intent_distribution': dict(intent_counts)
                }
            }
            processed_clusters.append(processed_cluster)
        
        # Sort clusters by total frequency (importance)
        processed_clusters.sort(key=lambda x: x['metrics']['total_frequency'], reverse=True)
        
        # Create summary statistics
        total_keywords = len(enriched_keywords)
        intent_distribution = defaultdict(int)
        for kw in enriched_keywords:
            intent_distribution[kw['search_intent']] += 1
        
        cluster_summary = {
            'total_clusters': len(processed_clusters),
            'total_keywords': total_keywords,
            'avg_keywords_per_cluster': round(total_keywords / len(processed_clusters), 1) if processed_clusters else 0,
            'largest_cluster_size': max(c['keyword_count'] for c in processed_clusters) if processed_clusters else 0
        }
        
        return {
            'clusters': processed_clusters,
            'cluster_summary': cluster_summary,
            'intent_distribution': dict(intent_distribution)
        }
        
    except Exception as e:
        return {
            'error': f"Failed to cluster keywords: {str(e)}",
            'clusters': [],
            'cluster_summary': {},
            'intent_distribution': {}
        }

# Example usage
if __name__ == "__main__":
    # Sample keywords for testing
    sample_keywords = [
        {'keyword': 'best seo tools', 'frequency': 15, 'content_score': 0.9},
        {'keyword': 'seo tools comparison', 'frequency': 12, 'content_score': 0.8},
        {'keyword': 'how to do keyword research', 'frequency': 10, 'content_score': 0.85},
        {'keyword': 'keyword research guide', 'frequency': 8, 'content_score': 0.75},
        {'keyword': 'buy seo software', 'frequency': 6, 'content_score': 0.7}
    ]
    
    result = cluster_keywords(sample_keywords)
    print(json.dumps(result, indent=2))
