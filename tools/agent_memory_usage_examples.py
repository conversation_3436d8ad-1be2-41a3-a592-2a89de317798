from smolagents.memory import <PERSON><PERSON><PERSON><PERSON>, TaskStep, PlanningStep, ActionStep
from smolagents.models import ChatMessage

def print_agent_memory_steps(agent_memory: AgentMemory):
    print("Agent Memory Steps:")
    for i, step in enumerate(agent_memory.steps):
        step_type = type(step).__name__
        content = ""
        if step_type == "PlanningStep":
            content = step.plan
        elif step_type == "TaskStep":
            content = step.task
        elif step_type == "ActionStep":
            content = step.model_output
        else:
            content = str(step)
        print(f"Step {i+1}: Type={step_type}, Content={content}")

def example_usage():
    # Create an AgentMemory instance with a system prompt
    agent_memory = AgentMemory(system_prompt="You are an assistant.")

    # Add a TaskStep
    task_step = TaskStep(task="Research sustainable gardening.")
    agent_memory.steps.append(task_step)

    # Add a PlanningStep
    planning_step = PlanningStep(
        model_input_messages=[],
        model_output_message=ChatMessage(role="assistant", content="Plan to gather data on urban gardening trends."),
        plan="Plan to gather data on urban gardening trends."
    )
    agent_memory.steps.append(planning_step)

    # Add an ActionStep
    action_step = ActionStep(
        model_input_messages=[],
        model_output_message=ChatMessage(role="assistant", content="Collected data on soil types suitable for urban gardens."),
        model_output="Collected data on soil types suitable for urban gardens."
    )
    agent_memory.steps.append(action_step)

    # Print all steps
    print_agent_memory_steps(agent_memory)

if __name__ == "__main__":
    example_usage()
