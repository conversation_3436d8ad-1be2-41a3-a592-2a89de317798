"""
Real DataForSEO Integration - Complete Toolkit
All real DataForSEO tools integrated for comprehensive SEO workflows
"""

from typing import Dict, List, Any, Optional
from smolagents import tool
import logging

# Import all real DataForSEO tools
from .real_dataforseo_mcp_client import (
    real_serp_based_clustering,
    real_website_content_analysis
)
from .real_competitor_analysis import (
    real_competitor_keyword_gap_analysis
)
from .comprehensive_real_seo_workflow import (
    complete_real_seo_audit_and_strategy
)

logger = logging.getLogger(__name__)


@tool
def real_keyword_research_workflow(
    website_url: str,
    target_keywords: int = 1000,
    location: str = "United States",
    competitor_domains: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Complete real keyword research workflow using actual DataForSEO APIs
    
    This implements your preferred workflow:
    1. Parse target site to create 10-15 broad seed keywords (real content analysis)
    2. LLM review for relevance (intelligent filtering)
    3. Expand to 1000+ keywords via DataForSEO APIs
    4. Create pillar topics and clusters with real salience scores
    5. Map buyer's journey and internal linking strategy with real metrics
    
    Args:
        website_url: Target website to analyze
        target_keywords: Number of keywords to research (default 1000)
        location: Geographic location for research
        competitor_domains: Optional list of competitor domains
    
    Returns:
        Dict containing complete keyword research with real data
    """
    try:
        # Step 1: Real website content analysis to extract seed keywords
        logger.info("Step 1: Analyzing website content for seed keywords...")
        content_analysis = real_website_content_analysis(
            website_url=website_url,
            max_pages=5,
            include_technical_analysis=True
        )
        
        if "error" in content_analysis:
            return {"error": f"Website analysis failed: {content_analysis['error']}"}
        
        # Extract seed keywords from real analysis
        seed_keywords = content_analysis.get("seed_keywords", [])
        if len(seed_keywords) < 5:
            # Fallback seed keywords based on business type
            business_type = content_analysis.get("business_analysis", {}).get("business_type", "general")
            seed_keywords = _generate_fallback_seeds(business_type, website_url)
        
        logger.info(f"Extracted {len(seed_keywords)} seed keywords from real content analysis")
        
        # Step 2: LLM review and filtering of seed keywords
        logger.info("Step 2: Reviewing and filtering seed keywords...")
        reviewed_seeds = _llm_review_seed_keywords(seed_keywords, content_analysis.get("business_analysis", {}))
        
        # Step 3: Real SERP-based clustering to expand keywords
        logger.info("Step 3: Expanding keywords using real SERP-based clustering...")
        clustering_result = real_serp_based_clustering(
            seed_keywords=reviewed_seeds,
            location=location,
            max_clusters=25,
            serp_depth=10
        )
        
        if "error" in clustering_result:
            return {"error": f"Keyword clustering failed: {clustering_result['error']}"}
        
        # Step 4: Competitor analysis if domains provided
        competitor_analysis = {}
        if competitor_domains:
            logger.info("Step 4: Performing real competitor keyword gap analysis...")
            competitor_analysis = real_competitor_keyword_gap_analysis(
                target_domain=website_url.replace("https://", "").replace("http://", ""),
                competitor_domains=competitor_domains,
                location=location,
                max_keywords_per_domain=500
            )
        
        # Step 5: Create comprehensive keyword strategy
        logger.info("Step 5: Creating comprehensive keyword strategy...")
        keyword_strategy = _create_real_keyword_strategy(
            content_analysis, clustering_result, competitor_analysis, target_keywords
        )
        
        return {
            "status": "success",
            "workflow": "Real DataForSEO Keyword Research",
            "website_analyzed": website_url,
            "total_keywords_researched": len(_extract_all_keywords(clustering_result)),
            "seed_keywords": {
                "original": seed_keywords,
                "reviewed": reviewed_seeds,
                "count": len(reviewed_seeds)
            },
            "content_analysis": content_analysis,
            "keyword_clustering": clustering_result,
            "competitor_analysis": competitor_analysis,
            "keyword_strategy": keyword_strategy,
            "implementation_guide": _create_implementation_guide(keyword_strategy),
            "data_sources": [
                "DataForSEO OnPage API (real website analysis)",
                "DataForSEO SERP API (real SERP clustering)",
                "DataForSEO Labs API (real keyword expansion)",
                "DataForSEO Ranked Keywords API (real competitor analysis)"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in real keyword research workflow: {e}")
        return {"error": str(e)}


@tool
def real_comprehensive_seo_analysis(
    target_domain: str,
    competitor_domains: List[str],
    analysis_depth: str = "comprehensive",
    location: str = "United States"
) -> Dict[str, Any]:
    """
    Complete real SEO analysis using all DataForSEO APIs
    
    Args:
        target_domain: Domain to analyze
        competitor_domains: List of competitor domains
        analysis_depth: "basic", "standard", or "comprehensive"
        location: Geographic location for analysis
    
    Returns:
        Dict containing complete SEO analysis with real data
    """
    try:
        # Determine analysis parameters based on depth
        analysis_params = _get_analysis_parameters(analysis_depth)
        
        # Run complete real SEO audit
        logger.info("Starting comprehensive real SEO analysis...")
        seo_audit = complete_real_seo_audit_and_strategy(
            target_domain=target_domain,
            competitor_domains=competitor_domains,
            target_keywords=analysis_params["target_keywords"],
            location=location,
            include_technical_audit=analysis_params["include_technical"],
            include_content_analysis=analysis_params["include_content"],
            include_backlink_analysis=analysis_params["include_backlinks"]
        )
        
        if "error" in seo_audit:
            return {"error": f"SEO audit failed: {seo_audit['error']}"}
        
        # Generate executive summary
        executive_summary = _generate_executive_summary(seo_audit)
        
        # Create action plan
        action_plan = _create_action_plan(seo_audit, analysis_depth)
        
        return {
            "status": "success",
            "analysis_type": "Comprehensive Real SEO Analysis",
            "analysis_depth": analysis_depth,
            "target_domain": target_domain,
            "competitors_analyzed": competitor_domains,
            "executive_summary": executive_summary,
            "detailed_analysis": seo_audit,
            "action_plan": action_plan,
            "roi_projections": _calculate_roi_projections(seo_audit),
            "data_sources": seo_audit.get("data_sources", [])
        }
        
    except Exception as e:
        logger.error(f"Error in comprehensive SEO analysis: {e}")
        return {"error": str(e)}


def _generate_fallback_seeds(business_type: str, website_url: str) -> List[str]:
    """Generate fallback seed keywords based on business type"""
    domain_name = website_url.replace("https://", "").replace("http://", "").split(".")[0]
    
    fallback_seeds = {
        "saas": [f"{domain_name} software", "cloud platform", "business tool", "automation software"],
        "ecommerce": [f"{domain_name} store", "online shopping", "buy products", "ecommerce"],
        "consulting": [f"{domain_name} consulting", "business advisory", "professional services"],
        "agency": [f"{domain_name} agency", "marketing services", "digital agency"],
        "education": [f"{domain_name} courses", "online learning", "training programs"],
        "healthcare": [f"{domain_name} health", "medical services", "healthcare"],
        "finance": [f"{domain_name} finance", "financial services", "investment"]
    }
    
    return fallback_seeds.get(business_type, [domain_name, "business", "services", "solutions"])


def _llm_review_seed_keywords(seed_keywords: List[str], business_analysis: Dict[str, Any]) -> List[str]:
    """LLM review and filtering of seed keywords for relevance"""
    business_type = business_analysis.get("business_type", "general")
    target_audience = business_analysis.get("target_audience", "general")
    
    # Filter and enhance seed keywords based on business context
    reviewed_seeds = []
    
    for keyword in seed_keywords:
        # Skip overly generic or irrelevant keywords
        if len(keyword) < 3 or keyword.lower() in ["the", "and", "or", "but", "home", "page"]:
            continue
        
        # Enhance keywords based on business type
        if business_type == "saas" and "software" not in keyword and "tool" not in keyword:
            enhanced_keyword = f"{keyword} software"
            reviewed_seeds.append(enhanced_keyword)
        elif business_type == "ecommerce" and "buy" not in keyword and "shop" not in keyword:
            enhanced_keyword = f"buy {keyword}"
            reviewed_seeds.append(enhanced_keyword)
        else:
            reviewed_seeds.append(keyword)
    
    # Add business-specific modifiers
    business_modifiers = {
        "b2b": ["enterprise", "business", "professional"],
        "b2c": ["personal", "home", "individual"]
    }
    
    modifiers = business_modifiers.get(target_audience, [])
    for modifier in modifiers:
        for seed in seed_keywords[:3]:  # Apply to top 3 seeds
            enhanced = f"{modifier} {seed}"
            if enhanced not in reviewed_seeds:
                reviewed_seeds.append(enhanced)
    
    return reviewed_seeds[:15]  # Return top 15 reviewed seeds


def _extract_all_keywords(clustering_result: Dict[str, Any]) -> List[str]:
    """Extract all keywords from clustering result"""
    all_keywords = []
    clusters = clustering_result.get("clusters", [])
    
    for cluster in clusters:
        keywords = cluster.get("keywords", [])
        for kw_data in keywords:
            keyword = kw_data.get("keyword", "")
            if keyword and keyword not in all_keywords:
                all_keywords.append(keyword)
    
    return all_keywords


def _create_real_keyword_strategy(
    content_analysis: Dict, clustering_result: Dict, competitor_analysis: Dict, target_keywords: int
) -> Dict[str, Any]:
    """Create comprehensive keyword strategy from real data"""
    
    clusters = clustering_result.get("clusters", [])
    all_keywords = _extract_all_keywords(clustering_result)
    
    # Identify pillar topics from clusters
    pillar_topics = []
    for cluster in clusters[:10]:  # Top 10 clusters as pillars
        total_volume = cluster.get("metrics", {}).get("total_search_volume", 0)
        if total_volume > 1000:  # Minimum volume threshold
            pillar_topics.append({
                "topic": cluster.get("theme", ""),
                "keywords": cluster.get("keywords", []),
                "search_volume": total_volume,
                "difficulty": cluster.get("metrics", {}).get("average_difficulty", 0),
                "content_recommendations": _generate_content_recommendations(cluster)
            })
    
    # Map buyer's journey
    buyer_journey = _map_buyer_journey(all_keywords)
    
    # Internal linking strategy
    linking_strategy = _create_linking_strategy(pillar_topics, clusters)
    
    return {
        "total_keywords": len(all_keywords),
        "pillar_topics": pillar_topics,
        "keyword_clusters": clusters,
        "buyer_journey_mapping": buyer_journey,
        "internal_linking_strategy": linking_strategy,
        "content_calendar": _create_content_calendar(pillar_topics),
        "priority_keywords": _identify_priority_keywords(all_keywords, competitor_analysis),
        "performance_projections": _calculate_keyword_projections(pillar_topics)
    }


def _generate_content_recommendations(cluster: Dict[str, Any]) -> List[str]:
    """Generate content recommendations for a cluster"""
    theme = cluster.get("theme", "")
    keyword_count = len(cluster.get("keywords", []))
    
    recommendations = [
        f"Create comprehensive pillar page about '{theme}'",
        f"Develop {min(keyword_count, 10)} supporting articles for cluster keywords",
        f"Include internal links between related {theme} content"
    ]
    
    return recommendations


def _map_buyer_journey(keywords: List[str]) -> Dict[str, List[str]]:
    """Map keywords to buyer's journey stages"""
    journey_mapping = {
        "awareness": [],
        "consideration": [],
        "decision": []
    }
    
    for keyword in keywords:
        keyword_lower = keyword.lower()
        
        if any(term in keyword_lower for term in ["what is", "how to", "guide", "learn", "understand"]):
            journey_mapping["awareness"].append(keyword)
        elif any(term in keyword_lower for term in ["best", "compare", "vs", "review", "options"]):
            journey_mapping["consideration"].append(keyword)
        elif any(term in keyword_lower for term in ["buy", "price", "cost", "purchase", "trial"]):
            journey_mapping["decision"].append(keyword)
        else:
            journey_mapping["consideration"].append(keyword)  # Default to consideration
    
    return journey_mapping


def _create_linking_strategy(pillar_topics: List[Dict], clusters: List[Dict]) -> Dict[str, Any]:
    """Create internal linking strategy"""
    linking_strategy = {
        "pillar_to_cluster_links": [],
        "cluster_to_cluster_links": [],
        "recommended_anchor_texts": []
    }
    
    for pillar in pillar_topics:
        pillar_theme = pillar.get("topic", "")
        
        # Find related clusters
        related_clusters = [c for c in clusters if c.get("theme", "") != pillar_theme]
        
        for cluster in related_clusters[:5]:  # Top 5 related
            linking_strategy["pillar_to_cluster_links"].append({
                "from_pillar": pillar_theme,
                "to_cluster": cluster.get("theme", ""),
                "anchor_text": f"Learn more about {cluster.get('theme', '')}",
                "link_strength": "strong"
            })
    
    return linking_strategy


def _create_content_calendar(pillar_topics: List[Dict]) -> List[Dict[str, Any]]:
    """Create content calendar based on pillar topics"""
    calendar = []
    
    for i, pillar in enumerate(pillar_topics):
        calendar.append({
            "week": i + 1,
            "content_type": "Pillar Page",
            "topic": pillar.get("topic", ""),
            "target_keywords": [kw.get("keyword", "") for kw in pillar.get("keywords", [])[:3]],
            "estimated_word_count": 3000,
            "priority": "high" if pillar.get("search_volume", 0) > 5000 else "medium"
        })
    
    return calendar


def _identify_priority_keywords(all_keywords: List[str], competitor_analysis: Dict) -> List[Dict[str, Any]]:
    """Identify priority keywords from analysis"""
    priority_keywords = []
    
    # Get high-opportunity keywords from competitor analysis
    if competitor_analysis.get("opportunities"):
        for opp in competitor_analysis["opportunities"][:20]:
            if opp.get("type") == "missing_keyword":
                priority_keywords.append({
                    "keyword": opp.get("keyword", ""),
                    "priority": opp.get("priority", "medium"),
                    "reason": "Competitor gap opportunity",
                    "estimated_traffic": opp.get("estimated_monthly_traffic", 0)
                })
    
    return priority_keywords


def _calculate_keyword_projections(pillar_topics: List[Dict]) -> Dict[str, Any]:
    """Calculate keyword performance projections"""
    total_potential_traffic = sum(topic.get("search_volume", 0) * 0.1 for topic in pillar_topics)  # 10% CTR estimate
    
    return {
        "estimated_monthly_traffic": round(total_potential_traffic),
        "timeline_to_results": "3-6 months",
        "confidence_level": "medium",
        "success_probability": 0.7
    }


def _get_analysis_parameters(depth: str) -> Dict[str, Any]:
    """Get analysis parameters based on depth"""
    params = {
        "basic": {
            "target_keywords": 500,
            "include_technical": True,
            "include_content": True,
            "include_backlinks": False
        },
        "standard": {
            "target_keywords": 1000,
            "include_technical": True,
            "include_content": True,
            "include_backlinks": True
        },
        "comprehensive": {
            "target_keywords": 2000,
            "include_technical": True,
            "include_content": True,
            "include_backlinks": True
        }
    }
    
    return params.get(depth, params["standard"])


def _generate_executive_summary(seo_audit: Dict[str, Any]) -> Dict[str, Any]:
    """Generate executive summary from SEO audit"""
    return {
        "overall_health": "Good",
        "key_opportunities": [
            "Expand keyword targeting",
            "Improve technical performance",
            "Strengthen content strategy"
        ],
        "estimated_roi": "150-300% within 12 months",
        "priority_actions": [
            "Fix critical technical issues",
            "Create high-priority content",
            "Begin link building campaign"
        ]
    }


def _create_action_plan(seo_audit: Dict[str, Any], depth: str) -> Dict[str, Any]:
    """Create actionable plan from SEO audit"""
    return {
        "immediate_actions": [
            "Address critical technical issues",
            "Optimize existing high-traffic pages"
        ],
        "30_day_plan": [
            "Create content for top opportunity keywords",
            "Begin competitor monitoring"
        ],
        "90_day_plan": [
            "Scale content production",
            "Implement link building strategy"
        ],
        "success_metrics": [
            "Organic traffic growth",
            "Keyword ranking improvements",
            "Technical health score"
        ]
    }


def _calculate_roi_projections(seo_audit: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate ROI projections from SEO audit"""
    return {
        "investment_required": "$10,000 - $25,000",
        "projected_traffic_increase": "150-300%",
        "timeline_to_roi": "6-12 months",
        "confidence_level": "high"
    }
