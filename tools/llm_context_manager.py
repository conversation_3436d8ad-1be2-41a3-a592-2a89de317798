from typing import List, Optional
from smolagents.memory import Agent<PERSON><PERSON><PERSON>, SystemPromptStep, TaskStep, PlanningStep, Message
from smolagents.models import ChatMessage

def manage_llm_context(
    system_prompt: str,
    site_context_facts: List[str],
    other_llm_contexts: Optional[List[AgentMemory]] = None,
    summary_mode: bool = False,
) -> List[Message]:
    """
    Abstract function to manage LLM context depending on site context and optionally other LLM contexts.
    It creates or updates an AgentMemory instance by stitching in site context facts and other LLM contexts.

    Args:
        system_prompt (str): The base system prompt for the agent.
        site_context_facts (List[str]): List of strings representing facts or context about the site.
        other_llm_contexts (Optional[List[AgentMemory]]): Optional list of other AgentMemory instances to stitch in.
        summary_mode (bool): Whether to generate messages in summary mode (default False).

    Returns:
        List[Message]: The concatenated prompt as a list of messages suitable for LLM input.
    """
    # Initialize AgentMemory with the base system prompt
    agent_memory = AgentMemory(system_prompt=system_prompt)

    # Add site context facts as PlanningSteps
    for fact in site_context_facts:
        planning_step = PlanningStep(
            model_input_messages=[],
            model_output_message=ChatMessage(role="assistant", content=fact),
            plan=fact
        )
        agent_memory.steps.append(planning_step)

    # Stitch in other LLM contexts if provided
    if other_llm_contexts:
        for other_memory in other_llm_contexts:
            # Append all steps from other memory to current memory
            agent_memory.steps.extend(other_memory.steps)

    # Convert all steps to messages
    messages: List[Message] = []
    # Add system prompt messages first
    messages.extend(agent_memory.system_prompt.to_messages(summary_mode=summary_mode))
    # Add messages from all steps
    for step in agent_memory.steps:
        messages.extend(step.to_messages(summary_mode=summary_mode))

    return messages


# Test example usage
if __name__ == "__main__":
    base_system_prompt = "You are an assistant helping with content strategy."
    site_facts = [
        "The site is about sustainable gardening.",
        "The target audience is urban gardeners.",
        "The main goal is to increase organic traffic."
    ]

    # Create a dummy other AgentMemory context for demonstration
    other_memory = AgentMemory(system_prompt="Previous conversation context.")
    other_memory.steps.append(TaskStep(task="Research keywords for gardening."))

    combined_messages = manage_llm_context(
        system_prompt=base_system_prompt,
        site_context_facts=site_facts,
        other_llm_contexts=[other_memory]
    )

    # Print out the combined messages for inspection
    for msg in combined_messages:
        print(f"Role: {msg['role']}, Content: {msg['content']}")
