"""
DataForSEO MCP Client Usage Examples
Demonstrates the improved expressive API with clear method names
"""

import asyncio
from enhanced_dataforseo_mcp_client import get_enhanced_client

async def example_content_analysis():
    """Example: Analyze website content with expressive method names"""
    client = get_enhanced_client()
    
    # Old way (generic and unclear):
    # result = await client.call_mcp_endpoint("/v3/on_page/content_parsing/live", {"url": "https://example.com"})
    
    # New way (expressive and clear):
    content_data = await client.contentParsing(
        "https://example.com",
        enable_javascript=True,
        enable_browser_rendering=True
    )
    
    print("Content Analysis Results:")
    print(f"Status: {content_data.get('status_code')}")
    if content_data.get('tasks'):
        items = content_data['tasks'][0].get('result', [{}])[0].get('items', [])
        if items:
            page_content = items[0].get('page_content', {})
            print(f"Title: {page_content.get('title')}")
            print(f"Word Count: {page_content.get('word_count')}")

async def example_keyword_research():
    """Example: Comprehensive keyword research workflow"""
    client = get_enhanced_client()
    
    # Expand a keyword cluster with expressive method
    cluster_data = await client.expandKeywordCluster("digital marketing", location="United States")
    
    print("Keyword Cluster Expansion:")
    print(f"Seed keyword: {cluster_data.get('seed_keyword')}")
    print(f"Analysis type: {cluster_data.get('analysis_type')}")
    
    # Get search intent with clear method name
    intent_data = await client.searchIntent(["buy shoes", "how to tie shoes", "best running shoes"])
    
    print("\nSearch Intent Analysis:")
    if intent_data.get('tasks'):
        items = intent_data['tasks'][0].get('result', [{}])[0].get('items', [])
        for item in items:
            keyword = item.get('keyword')
            intent = item.get('keyword_intent', {})
            print(f"'{keyword}' -> {intent.get('label')} ({intent.get('probability', 0):.2f})")

async def example_competitor_analysis():
    """Example: Analyze competitor with single method call"""
    client = get_enhanced_client()
    
    # Old way (multiple API calls, complex setup):
    # categories = await client.call_mcp_endpoint("/v3/dataforseo_labs/google/categories_for_domain/live", {...})
    # keywords = await client.call_mcp_endpoint("/v3/dataforseo_labs/google/ranked_keywords/live", {...})
    
    # New way (single expressive method):
    competitor_data = await client.analyzeCompetitor("competitor.com", location="United States")
    
    print("Competitor Analysis:")
    print(f"Domain: {competitor_data.get('domain')}")
    print(f"Analysis type: {competitor_data.get('analysis_type')}")
    
    # Categories analysis
    if competitor_data.get('categories', {}).get('tasks'):
        categories = competitor_data['categories']['tasks'][0].get('result', [{}])[0].get('items', [])
        print(f"Top category: {categories[0].get('category_name') if categories else 'Unknown'}")

async def example_serp_analysis():
    """Example: SERP analysis with clear method names"""
    client = get_enhanced_client()
    
    # Get SERP results with expressive method
    serp_data = await client.serpOrganic(
        "best seo tools",
        location="United States",
        depth=10
    )
    
    print("SERP Analysis:")
    if serp_data.get('tasks'):
        items = serp_data['tasks'][0].get('result', [{}])[0].get('items', [])
        print(f"Found {len(items)} organic results")
        
        for i, item in enumerate(items[:5], 1):
            if item.get('type') == 'organic':
                print(f"{i}. {item.get('title')} - {item.get('domain')}")

async def example_bulk_operations():
    """Example: Bulk keyword difficulty analysis"""
    client = get_enhanced_client()
    
    keywords = [
        "seo tools",
        "keyword research",
        "content marketing",
        "digital marketing",
        "social media marketing"
    ]
    
    # Bulk keyword difficulty with expressive method
    difficulty_data = await client.bulkKeywordDifficulty(keywords)
    
    print("Bulk Keyword Difficulty:")
    if difficulty_data.get('tasks'):
        items = difficulty_data['tasks'][0].get('result', [{}])[0].get('items', [])
        for item in items:
            keyword = item.get('keyword')
            difficulty = item.get('keyword_difficulty', 0)
            print(f"'{keyword}': {difficulty}/100 difficulty")

async def main():
    """Run all examples"""
    print("=== DataForSEO MCP Client Examples ===\n")
    
    try:
        await example_content_analysis()
        print("\n" + "="*50 + "\n")
        
        await example_keyword_research()
        print("\n" + "="*50 + "\n")
        
        await example_competitor_analysis()
        print("\n" + "="*50 + "\n")
        
        await example_serp_analysis()
        print("\n" + "="*50 + "\n")
        
        await example_bulk_operations()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure to set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD environment variables")

if __name__ == "__main__":
    # Set up credentials (you can also set these as environment variables)
    # os.environ['DATAFORSEO_USERNAME'] = 'your_username'
    # os.environ['DATAFORSEO_PASSWORD'] = 'your_password'
    
    asyncio.run(main())
