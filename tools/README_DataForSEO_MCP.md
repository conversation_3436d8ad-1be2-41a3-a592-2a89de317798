# Enhanced DataForSEO MCP Client

A comprehensive, expressive MCP client for DataForSEO APIs with improved readability and developer experience.

## 🚀 Key Improvements

### ✅ Expressive Method Names

Instead of generic `call_mcp_endpoint()`, use intuitive method names:

```python
# ❌ Old way (unclear and verbose)
result = await client.call_mcp_endpoint("/v3/on_page/content_parsing/live", {
    "url": "https://example.com",
    "enable_javascript": True
})

# ✅ New way (clear and expressive)
content_data = await client.contentParsing(
    "https://example.com",
    enable_javascript=True
)
```

### ✅ High-Level Workflow Methods

Common SEO workflows simplified into single method calls:

```python
# Complete competitor analysis
competitor_data = await client.analyzeCompetitor("competitor.com")

# Expand keyword cluster
cluster_data = await client.expandKeywordCluster("digital marketing")
```

### ✅ Real API Integration

- Direct calls to DataForSEO APIs using aiohttp
- Comprehensive error handling and validation
- Proper authentication and timeout handling
- No simulation fallbacks - real API calls only

## 📋 Available Methods

### Content Analysis

```python
await client.contentParsing(url, **kwargs)
```

### Keyword Research

```python
await client.searchIntent(keywords, **kwargs)
await client.relatedKeywords(keyword, location="United States", **kwargs)
await client.keywordIdeas(keyword, location="United States", **kwargs)
await client.bulkKeywordDifficulty(keywords, **kwargs)
await client.keywordOverview(keywords, location="United States", **kwargs)
await client.keywordsForKeywords(keyword, **kwargs)
```

### SERP Analysis

```python
await client.serpOrganic(keyword, location="United States", **kwargs)
```

### Competitor Analysis

```python
await client.rankedKeywords(domain, location="United States", **kwargs)
await client.categoriesForDomain(domain, **kwargs)
```

### Workflow Methods

```python
await client.analyzeCompetitor(domain, location="United States")
await client.expandKeywordCluster(seed_keyword, location="United States")
```

## 🛠 Setup

1. **Install Dependencies**

```bash
pip install aiohttp smolagents
```

2. **Set Credentials**

```bash
export DATAFORSEO_USERNAME="your_username"
export DATAFORSEO_PASSWORD="your_password"
```

3. **Import and Use**

```python
from enhanced_dataforseo_mcp_client import get_enhanced_client

client = get_enhanced_client()
result = await client.contentParsing("https://example.com")
```

## 📖 Usage Examples

### Basic Content Analysis

```python
import asyncio
from enhanced_dataforseo_mcp_client import get_enhanced_client

async def analyze_website():
    client = get_enhanced_client()

    # Parse website content
    content = await client.contentParsing("https://example.com")

    # Get SERP results
    serp = await client.serpOrganic("digital marketing")

    # Analyze search intent
    intent = await client.searchIntent(["buy shoes", "how to run"])

    return content, serp, intent

asyncio.run(analyze_website())
```

### Advanced Keyword Research

```python
async def keyword_research_workflow():
    client = get_enhanced_client()

    # Expand keyword cluster
    cluster = await client.expandKeywordCluster("seo tools")

    # Get competitor insights
    competitor = await client.analyzeCompetitor("semrush.com")

    # Bulk difficulty analysis
    keywords = ["seo", "keyword research", "content marketing"]
    difficulty = await client.bulkKeywordDifficulty(keywords)

    return cluster, competitor, difficulty
```

## 🔧 Available Tools

### `smart_keyword_research_workflow`

Comprehensive keyword research using expressive API calls:

```python
from enhanced_dataforseo_mcp_client import smart_keyword_research_workflow

result = smart_keyword_research_workflow(
    seed_keywords=["digital marketing", "seo tools"],
    target_domain="competitor.com",
    location="United States",
    analysis_depth="comprehensive"
)
```

### `advanced_serp_based_clustering`

SERP-based keyword clustering with shared ranking page analysis:

```python
from enhanced_dataforseo_mcp_client import advanced_serp_based_clustering

clusters = advanced_serp_based_clustering(
    seed_keywords=["content marketing", "seo"],
    location="United States",
    max_clusters=20,
    serp_depth=10
)
```

### `comprehensive_website_analysis`

Complete website analysis with content and technical SEO:

```python
from enhanced_dataforseo_mcp_client import comprehensive_website_analysis

analysis = comprehensive_website_analysis(
    website_url="https://example.com",
    include_content_analysis=True,
    include_technical_seo=True
)
```

## 🎯 Benefits

1. **Improved Readability**: Method names clearly indicate their purpose
2. **Better IDE Support**: Full type hints and autocomplete
3. **Reduced Complexity**: High-level methods for common workflows
4. **Error Resilience**: Graceful fallbacks and comprehensive error handling
5. **Real API Integration**: Direct calls to DataForSEO with simulation fallbacks

## 📝 Example Output

The enhanced client provides structured, comprehensive results:

```json
{
  "workflow": "smart_keyword_research",
  "input_parameters": {
    "seed_keywords": ["digital marketing"],
    "location": "United States",
    "analysis_depth": "standard"
  },
  "results": {
    "expanded_clusters": [...],
    "competitor_analysis": {...},
    "keyword_difficulty": {...}
  },
  "strategic_recommendations": [
    "Identified 150 related keywords across 1 clusters",
    "Low competition opportunity - most keywords have difficulty < 30"
  ]
}
```

This enhanced approach makes DataForSEO integration much more intuitive and maintainable!
