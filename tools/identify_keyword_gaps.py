"""
Keyword Gap Analysis Tool
Identifies keyword opportunities by comparing user keywords with competitor keywords
"""

from smolagents import tool
from typing import List, Dict, Any
import json

@tool
def identify_keyword_gaps(user_keywords: List[Dict], competitor_keywords: List[Dict]) -> Dict[str, Any]:
    """
    Analyzes keyword gaps between user's keywords and competitor keywords.
    Identifies opportunities where competitors rank but user doesn't.
    
    Args:
        user_keywords (List[Dict]): Keywords extracted from user's page
        competitor_keywords (List[Dict]): Combined keywords from all competitors
        
    Returns:
        Dict containing:
        - keyword_gaps: Keywords competitors have but user doesn't
        - user_advantages: Keywords user has but competitors don't
        - overlap: Common keywords between user and competitors
        - gap_analysis: Detailed analysis and recommendations
    """
    
    def normalize_keyword(keyword: str) -> str:
        """Normalize keyword for comparison"""
        return keyword.lower().strip()
    
    def calculate_keyword_value(keyword_data: Dict) -> float:
        """Calculate a value score for a keyword based on metrics"""
        frequency = keyword_data.get('frequency', 0)
        density = keyword_data.get('density', 0)
        content_score = keyword_data.get('content_score', 0)
        
        # Weight the different factors
        value_score = (
            frequency * 0.4 +  # Frequency is important but not everything
            density * 0.3 +    # Density shows prominence
            content_score * 0.3 # Content score shows relevance
        )
        return round(value_score, 2)
    
    try:
        # Create sets of normalized keywords for comparison
        user_keyword_set = {normalize_keyword(k['keyword']): k for k in user_keywords}
        competitor_keyword_set = {normalize_keyword(k['keyword']): k for k in competitor_keywords}
        
        # Find gaps and overlaps
        user_only_keywords = set(user_keyword_set.keys()) - set(competitor_keyword_set.keys())
        competitor_only_keywords = set(competitor_keyword_set.keys()) - set(user_keyword_set.keys())
        common_keywords = set(user_keyword_set.keys()) & set(competitor_keyword_set.keys())
        
        # Analyze gaps in detail
        keyword_gaps = []
        for keyword in competitor_only_keywords:
            keyword_data = competitor_keyword_set[keyword]
            value_score = calculate_keyword_value(keyword_data)
            keyword_gaps.append({
                'keyword': keyword,
                'value_score': value_score,
                'competitor_metrics': keyword_data,
                'opportunity_level': 'high' if value_score > 0.7 else 'medium' if value_score > 0.4 else 'low'
            })
        
        # Sort gaps by value score
        keyword_gaps.sort(key=lambda x: x['value_score'], reverse=True)
        
        # Analyze user's advantages
        user_advantages = []
        for keyword in user_only_keywords:
            keyword_data = user_keyword_set[keyword]
            value_score = calculate_keyword_value(keyword_data)
            user_advantages.append({
                'keyword': keyword,
                'value_score': value_score,
                'user_metrics': keyword_data
            })
        
        # Sort advantages by value score
        user_advantages.sort(key=lambda x: x['value_score'], reverse=True)
        
        # Analyze overlap
        overlap_analysis = []
        for keyword in common_keywords:
            user_data = user_keyword_set[keyword]
            competitor_data = competitor_keyword_set[keyword]
            user_score = calculate_keyword_value(user_data)
            competitor_score = calculate_keyword_value(competitor_data)
            
            overlap_analysis.append({
                'keyword': keyword,
                'user_score': user_score,
                'competitor_score': competitor_score,
                'difference': round(user_score - competitor_score, 2),
                'user_metrics': user_data,
                'competitor_metrics': competitor_data
            })
        
        # Sort overlap by absolute difference
        overlap_analysis.sort(key=lambda x: abs(x['difference']), reverse=True)
        
        # Generate gap analysis summary
        gap_analysis = {
            'total_gaps_found': len(keyword_gaps),
            'high_value_opportunities': len([k for k in keyword_gaps if k['opportunity_level'] == 'high']),
            'user_advantages_count': len(user_advantages),
            'overlap_count': len(overlap_analysis),
            'recommended_priorities': [k['keyword'] for k in keyword_gaps[:5] if k['opportunity_level'] in ['high', 'medium']],
            'competitive_advantages': [k['keyword'] for k in user_advantages[:5]]
        }
        
        return {
            'keyword_gaps': keyword_gaps,
            'user_advantages': user_advantages,
            'overlap': overlap_analysis,
            'gap_analysis': gap_analysis
        }
        
    except Exception as e:
        return {
            'error': f"Failed to analyze keyword gaps: {str(e)}",
            'keyword_gaps': [],
            'user_advantages': [],
            'overlap': [],
            'gap_analysis': {}
        }

# Example usage
if __name__ == "__main__":
    # Sample data for testing
    user_keywords = [
        {'keyword': 'seo guide', 'frequency': 10, 'density': 2.5, 'content_score': 0.8},
        {'keyword': 'keyword research', 'frequency': 8, 'density': 1.8, 'content_score': 0.9}
    ]
    competitor_keywords = [
        {'keyword': 'seo guide', 'frequency': 12, 'density': 2.8, 'content_score': 0.85},
        {'keyword': 'seo tools', 'frequency': 15, 'density': 3.0, 'content_score': 0.95}
    ]
    
    result = identify_keyword_gaps(user_keywords, competitor_keywords)
    print(json.dumps(result, indent=2))
