#!/usr/bin/env python3
"""
Test script for DataForSEO MCP integration
This script tests the MCP tools and verifies the integration is working
"""

import os
import sys

def test_environment_setup():
    """Test if environment variables are properly set"""
    print("🔧 Testing Environment Setup...")
    
    username = os.getenv('DATAFORSEO_USERNAME')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username:
        print("❌ DATAFORSEO_USERNAME not set")
        return False
    
    if not password:
        print("❌ DATAFORSEO_PASSWORD not set")
        return False
    
    print(f"✅ DATAFORSEO_USERNAME: {username}")
    print("✅ DATAFORSEO_PASSWORD: [HIDDEN]")
    return True

def test_mcp_tools():
    """Test the enhanced MCP tools"""
    print("\n🧪 Testing Enhanced MCP Tools...")

    # Test website content analysis
    print("\n1. Testing Website Content Analysis...")
    try:
        from tools.dataforseo_mcp_client import analyze_website_content
        result = analyze_website_content("hassconsult.com", 3)
        print("✅ Website Analysis tool imported and executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:200]}...")
    except Exception as e:
        print(f"❌ Website Analysis error: {e}")

    # Test keyword expansion
    print("\n2. Testing Keyword Expansion...")
    try:
        from tools.dataforseo_mcp_client import expand_seed_keywords
        result = expand_seed_keywords(["seo", "marketing"], 50, "United States")
        print("✅ Keyword Expansion tool imported and executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:200]}...")
    except Exception as e:
        print(f"❌ Keyword Expansion error: {e}")

    # Test keyword clustering
    print("\n3. Testing Keyword Clustering...")
    try:
        from tools.dataforseo_mcp_client import cluster_and_analyze_keywords
        sample_keywords = [
            {"keyword": "seo tools", "search_volume": 1000, "difficulty": 45},
            {"keyword": "keyword research", "search_volume": 800, "difficulty": 50},
            {"keyword": "content marketing", "search_volume": 1200, "difficulty": 40}
        ]
        result = cluster_and_analyze_keywords(sample_keywords)
        print("✅ Keyword Clustering tool imported and executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:200]}...")
    except Exception as e:
        print(f"❌ Keyword Clustering error: {e}")

    # Test comprehensive workflow
    print("\n4. Testing Comprehensive Workflow...")
    try:
        from tools.dataforseo_mcp_client import comprehensive_keyword_research
        result = comprehensive_keyword_research("hassconsult.com", 100, "United States")
        print("✅ Comprehensive Workflow tool imported and executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:200]}...")
    except Exception as e:
        print(f"❌ Comprehensive Workflow error: {e}")

    # Test autonomous agent
    print("\n5. Testing Autonomous MCP Agent...")
    try:
        from tools.autonomous_mcp_agent import autonomous_mcp_call
        result = autonomous_mcp_call("Find related keywords for 'digital marketing' with search volume data")
        print("✅ Autonomous Agent tool imported and executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result preview: {str(result)[:200]}...")
    except Exception as e:
        print(f"❌ Autonomous Agent error: {e}")

def test_agent_integration():
    """Test the agent with MCP tools"""
    print("\n🤖 Testing Agent Integration...")
    
    try:
        from app import agent
        
        # Test a simple query
        print("Testing agent with MCP tools...")
        
        # This is a basic test - in practice, you'd run more comprehensive tests
        print("✅ Agent imported successfully")
        print(f"   Agent tools: {len(agent.tools)} tools available")
        
        # List the tools
        tool_names = []
        for tool in agent.tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
        
        print(f"   Tool names: {tool_names}")
        
        # Check if MCP tools are included
        mcp_tools = [name for name in tool_names if 'mcp_' in name]
        if mcp_tools:
            print(f"✅ MCP tools found: {mcp_tools}")
        else:
            print("❌ No MCP tools found in agent")
        
    except Exception as e:
        print(f"❌ Agent integration error: {e}")

def run_sample_analysis():
    """Run a sample SEO analysis using MCP tools"""
    print("\n📊 Running Sample SEO Analysis...")

    try:
        # Sample SERP analysis
        print("1. Testing SERP analysis for 'seo tools'...")
        from tools.dataforseo_mcp_client import mcp_serp_analysis
        serp_result = mcp_serp_analysis("seo tools", "United States")
        print("✅ SERP analysis tool executed")
        print(f"   Result type: {type(serp_result)}")

        # Sample competitor analysis
        print("\n2. Testing competitor analysis for 'semrush.com'...")
        from tools.dataforseo_mcp_client import mcp_competitor_analysis
        competitor_result = mcp_competitor_analysis("semrush.com")
        print("✅ Competitor analysis tool executed")
        print(f"   Result type: {type(competitor_result)}")

        # Sample comprehensive workflow
        print("\n3. Testing comprehensive keyword research workflow...")
        from tools.dataforseo_mcp_client import comprehensive_keyword_research
        workflow_result = comprehensive_keyword_research("hassconsult.com", 50, "United States")
        print("✅ Comprehensive workflow executed")
        print(f"   Result type: {type(workflow_result)}")

        print("\n📋 Sample Analysis Summary:")
        print("- All MCP tools are functional and importable")
        print("- Tools execute without import errors")
        print("- Ready for agent integration testing")
        print("- Integration framework is working correctly")

    except Exception as e:
        print(f"❌ Sample analysis error: {e}")

def main():
    """Main test function"""
    print("🚀 DataForSEO MCP Integration Test")
    print("=" * 50)
    
    # Test environment setup
    if not test_environment_setup():
        print("\n❌ Environment setup failed. Please run setup_dataforseo_mcp.py first.")
        sys.exit(1)
    
    # Test MCP tools
    test_mcp_tools()
    
    # Test agent integration
    test_agent_integration()
    
    # Run sample analysis
    run_sample_analysis()
    
    print("\n" + "=" * 50)
    print("🎉 Integration test completed!")
    print("\n📋 Next steps:")
    print("1. If all tests passed, your integration is ready")
    print("2. You can now use MCP tools in your SEO agent")
    print("3. Try running your agent with MCP-enhanced queries")
    
    print("\n💡 Example agent queries to try:")
    print('- "Analyze the SERP for \'digital marketing tools\' using MCP"')
    print('- "Research keywords for \'seo audit\' using MCP tools"')
    print('- "Perform competitor analysis on \'ahrefs.com\' using MCP"')

if __name__ == "__main__":
    main()
