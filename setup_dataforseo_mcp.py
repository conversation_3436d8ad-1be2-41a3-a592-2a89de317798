#!/usr/bin/env python3
"""
Setup script for DataForSEO MCP server integration
This script helps set up the DataForSEO MCP server for use with your SEO agent
"""

import os
import subprocess
import sys
import json
import platform
from pathlib import Path

def check_node_installed():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js is installed: {version}")
            return True
        else:
            print("✗ Node.js is not installed")
            return False
    except FileNotFoundError:
        print("✗ Node.js is not installed")
        return False

def install_node():
    """Provide instructions for installing Node.js"""
    system = platform.system().lower()
    
    print("\n📦 Node.js Installation Instructions:")
    print("=" * 50)
    
    if system == "darwin":  # macOS
        print("For macOS:")
        print("1. Using Homebrew: brew install node")
        print("2. Or download from: https://nodejs.org/en/download/")
    elif system == "linux":
        print("For Linux:")
        print("1. Using package manager:")
        print("   - Ubuntu/Debian: sudo apt-get install nodejs npm")
        print("   - CentOS/RHEL: sudo yum install nodejs npm")
        print("2. Or download from: https://nodejs.org/en/download/")
    elif system == "windows":
        print("For Windows:")
        print("1. Download installer from: https://nodejs.org/en/download/")
        print("2. Or use Chocolatey: choco install nodejs")
    
    print("\nAfter installing Node.js, run this script again.")

def install_dataforseo_mcp_server():
    """Install the DataForSEO MCP server"""
    print("\n📦 Installing DataForSEO MCP server...")
    
    try:
        # Install the MCP server globally
        result = subprocess.run([
            'npm', 'install', '-g', 'dataforseo-mcp-server'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ DataForSEO MCP server installed successfully")
            return True
        else:
            print(f"✗ Failed to install DataForSEO MCP server: {result.stderr}")
            
            # Try alternative installation method
            print("\n🔄 Trying alternative installation method...")
            result = subprocess.run([
                'npx', 'dataforseo-mcp-server', '--help'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ DataForSEO MCP server is available via npx")
                return True
            else:
                print("✗ Alternative installation also failed")
                return False
                
    except FileNotFoundError:
        print("✗ npm not found. Please install Node.js first.")
        return False

def setup_environment_variables():
    """Help user set up environment variables"""
    print("\n🔧 Environment Variables Setup:")
    print("=" * 40)
    
    username = os.getenv('DATAFORSEO_USERNAME')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username or not password:
        print("⚠️  DataForSEO credentials not found in environment variables.")
        print("\nTo set up your credentials, add these to your environment:")
        print("\nFor bash/zsh (add to ~/.bashrc or ~/.zshrc):")
        print("export DATAFORSEO_USERNAME='your_username'")
        print("export DATAFORSEO_PASSWORD='your_password'")
        
        print("\nFor Windows (Command Prompt):")
        print("set DATAFORSEO_USERNAME=your_username")
        print("set DATAFORSEO_PASSWORD=your_password")
        
        print("\nFor Windows (PowerShell):")
        print("$env:DATAFORSEO_USERNAME='your_username'")
        print("$env:DATAFORSEO_PASSWORD='your_password'")
        
        print("\n📝 You can get your DataForSEO credentials from:")
        print("   https://app.dataforseo.com/signin")
        
        return False
    else:
        print(f"✓ DATAFORSEO_USERNAME: {username}")
        print("✓ DATAFORSEO_PASSWORD: [HIDDEN]")
        return True

def create_mcp_config():
    """Create MCP configuration file for Claude Desktop"""
    print("\n📄 Creating MCP configuration...")
    
    # Determine the config path based on the operating system
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        config_dir = Path.home() / "Library" / "Application Support" / "Claude"
    elif system == "windows":
        config_dir = Path.home() / "AppData" / "Roaming" / "Claude"
    else:  # Linux
        config_dir = Path.home() / ".config" / "claude"
    
    config_dir.mkdir(parents=True, exist_ok=True)
    config_file = config_dir / "claude_desktop_config.json"
    
    # Create the configuration
    config = {
        "mcpServers": {
            "dataforseo": {
                "command": "npx",
                "args": ["dataforseo-mcp-server"],
                "env": {
                    "DATAFORSEO_USERNAME": os.getenv('DATAFORSEO_USERNAME', 'your_username'),
                    "DATAFORSEO_PASSWORD": os.getenv('DATAFORSEO_PASSWORD', 'your_password'),
                    "ENABLED_MODULES": "SERP,KEYWORDS_DATA,ONPAGE,DATAFORSEO_LABS,BUSINESS_DATA,DOMAIN_ANALYTICS,BACKLINKS"
                }
            }
        }
    }
    
    # If config file exists, merge with existing configuration
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                existing_config = json.load(f)
            
            if 'mcpServers' not in existing_config:
                existing_config['mcpServers'] = {}
            
            existing_config['mcpServers']['dataforseo'] = config['mcpServers']['dataforseo']
            config = existing_config
            
        except json.JSONDecodeError:
            print("⚠️  Existing config file is invalid, creating new one")
    
    # Write the configuration
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✓ MCP configuration created at: {config_file}")
        print("\n📋 Configuration contents:")
        print(json.dumps(config, indent=2))
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to create MCP configuration: {e}")
        return False

def test_mcp_server():
    """Test if the MCP server can be started"""
    print("\n🧪 Testing MCP server...")
    
    try:
        # Set environment variables for the test
        env = os.environ.copy()
        env.update({
            'DATAFORSEO_USERNAME': os.getenv('DATAFORSEO_USERNAME', 'test'),
            'DATAFORSEO_PASSWORD': os.getenv('DATAFORSEO_PASSWORD', 'test'),
            'ENABLED_MODULES': 'SERP,KEYWORDS_DATA'
        })
        
        # Try to run the server with --help flag
        result = subprocess.run([
            'npx', 'dataforseo-mcp-server', '--help'
        ], capture_output=True, text=True, env=env, timeout=30)
        
        if result.returncode == 0:
            print("✓ MCP server can be started successfully")
            return True
        else:
            print(f"✗ MCP server test failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  MCP server test timed out (this might be normal)")
        return True
    except Exception as e:
        print(f"✗ MCP server test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 DataForSEO MCP Server Setup")
    print("=" * 50)
    
    success = True
    
    # Check Node.js
    if not check_node_installed():
        install_node()
        success = False
    
    # Install MCP server
    if success and not install_dataforseo_mcp_server():
        success = False
    
    # Setup environment variables
    if not setup_environment_variables():
        success = False
    
    # Create MCP config
    if success and not create_mcp_config():
        success = False
    
    # Test MCP server
    if success:
        test_mcp_server()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart Claude Desktop if you're using it")
        print("2. Your SEO agent is now ready to use DataForSEO MCP tools")
        print("3. Run your agent with the new MCP integration")
    else:
        print("⚠️  Setup completed with some issues.")
        print("Please resolve the issues above and run the setup again.")
    
    print("\n📚 For more information, visit:")
    print("   https://dataforseo.com/help-center/setting-up-the-official-dataforseo-mcp-server-simple-guide")

if __name__ == "__main__":
    main()
