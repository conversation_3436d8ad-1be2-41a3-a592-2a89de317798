# Keyword Research Agent Examples

This document provides comprehensive examples of how to use the keyword research agent with the specialized SEO tools.

## Available Tools

1. **keyword_difficulty_tool** - Analyzes keyword competition and difficulty
2. **domain_rating_tool** - Checks domain authority and backlink profile
3. **page_rank_tool** - Analyzes page authority and ranking potential
4. **keyword_fetcher_tool** - Extracts keywords from webpages

## Example Workflows

### Example 1: New Fitness Blog Keyword Research

**Task**: "I'm starting a new fitness blog focused on yoga. Help me find keyword opportunities that a new site can realistically rank for."

**Expected Agent Workflow**:

```python
# Step 1: Analyze competitor pages to extract keywords
competitor_keywords = keyword_fetcher_tool("https://yogajournal.com/practice/")
print("Competitor keywords found:", competitor_keywords)

# Step 2: Check competitor domain strength
competitor_domain = domain_rating_tool("yogajournal.com")
print("Competitor domain rating:", competitor_domain)

# Step 3: Analyze keyword difficulty for extracted keywords
viable_keywords = []
for keyword_data in competitor_keywords[:10]:
    keyword = keyword_data['keyword']
    difficulty = keyword_difficulty_tool(keyword)
    if difficulty['difficulty_score'] < 40:  # Focus on easier keywords
        viable_keywords.append({
            'keyword': keyword,
            'difficulty': difficulty,
            'relevance': keyword_data['relevance']
        })

print("Viable keywords for new site:", viable_keywords)
```

**Expected Output Structure**:

- Keywords with difficulty scores under 40
- Long-tail variations like "beginner yoga poses for flexibility"
- Content gap opportunities
- Recommendations prioritized by difficulty vs. relevance

### Example 2: E-commerce Product Research

**Task**: "Analyze keyword opportunities for selling organic pet food online. Focus on commercial intent keywords."

**Expected Agent Workflow**:

```python
# Step 1: Analyze major competitor
competitor_analysis = domain_rating_tool("chewy.com")
print("Major competitor strength:", competitor_analysis)

# Step 2: Find keywords from smaller competitors
smaller_competitor_keywords = keyword_fetcher_tool("https://naturalpetfood.com/products/")

# Step 3: Filter for commercial intent
commercial_keywords = []
for keyword_data in smaller_competitor_keywords:
    if keyword_data['search_intent'] in ['commercial', 'transactional']:
        difficulty = keyword_difficulty_tool(keyword_data['keyword'])
        commercial_keywords.append({
            'keyword': keyword_data['keyword'],
            'difficulty': difficulty,
            'intent': keyword_data['search_intent']
        })

# Step 4: Prioritize based on difficulty and commercial value
final_recommendations = sorted(commercial_keywords,
                             key=lambda x: (x['difficulty']['difficulty_score'], -x['difficulty']['search_volume']))
```

### Example 3: Local Service Business

**Task**: "Help a local plumbing service in Austin find keywords to compete against national brands."

**Expected Agent Workflow**:

```python
# Step 1: Analyze national competitor strength
national_competitor = domain_rating_tool("roto-rooter.com")
local_competitor = domain_rating_tool("austinplumber.com")

# Step 2: Extract location-based keywords
local_keywords = keyword_fetcher_tool("https://austinplumber.com/services/")

# Step 3: Focus on local + service combinations
local_opportunities = []
for keyword_data in local_keywords:
    if 'austin' in keyword_data['keyword'].lower() or 'local' in keyword_data['keyword'].lower():
        difficulty = keyword_difficulty_tool(keyword_data['keyword'])
        local_opportunities.append({
            'keyword': keyword_data['keyword'],
            'difficulty': difficulty,
            'local_relevance': True
        })

# Step 4: Identify service-specific long-tail opportunities
service_keywords = [
    "emergency plumber austin",
    "austin drain cleaning service",
    "water heater repair austin tx"
]

for keyword in service_keywords:
    difficulty = keyword_difficulty_tool(keyword)
    local_opportunities.append({
        'keyword': keyword,
        'difficulty': difficulty,
        'service_specific': True
    })
```

## Strategic Recommendations Framework

### For New/Low Authority Sites (DR < 30)

- Target keywords with difficulty < 30
- Focus on long-tail keywords (4+ words)
- Look for question-based keywords
- Prioritize informational content opportunities

### For Medium Authority Sites (DR 30-60)

- Target keywords with difficulty 30-50
- Mix of informational and commercial content
- Consider seasonal opportunities
- Build topic clusters around main keywords

### For High Authority Sites (DR 60+)

- Can target competitive keywords (difficulty 50-70)
- Focus on high-volume commercial terms
- Compete for industry head terms
- Target featured snippet opportunities

## Common Keyword Research Patterns

### Content Gap Analysis

1. Extract keywords from top 3 competitors
2. Identify keywords they rank for but you don't
3. Filter by difficulty appropriate for your domain
4. Prioritize by search volume and business relevance

### Competitor Weakness Exploitation

1. Find competitors with lower domain ratings
2. Analyze their top-performing content
3. Identify keywords they rank well for
4. Create superior content targeting same keywords

### Long-tail Opportunity Mining

1. Start with broad seed keywords
2. Use keyword fetcher on relevant pages
3. Filter for 4+ word phrases
4. Focus on question-based queries
5. Target specific user intents

## Integration with Content Strategy

### Content Planning

- Group related keywords into topic clusters
- Plan content calendar around keyword priorities
- Consider seasonal trends and timing
- Balance difficulty with publishing capacity

### Performance Tracking

- Monitor ranking improvements for targeted keywords
- Track organic traffic growth
- Measure conversion rates by keyword intent
- Adjust strategy based on performance data

## Best Practices

1. **Always start with competitor analysis** - Understand the competitive landscape
2. **Consider user intent** - Match content to search intent
3. **Balance difficulty with opportunity** - Don't just chase easy keywords
4. **Think long-term** - Build authority gradually
5. **Monitor and adjust** - SEO is an iterative process
6. **Focus on business value** - Prioritize keywords that drive conversions
7. **Create comprehensive content** - Target multiple related keywords per page
8. **Build topic authority** - Become the go-to source for your niche

## Sample Agent Responses

### Quick Win Recommendations

- "Based on analysis, target 'yoga poses for back pain relief' (difficulty: 28, volume: 2,400)"
- "Opportunity found: 'best organic dog food for puppies' has manageable competition"
- "Local advantage: 'austin emergency plumber' shows 40% less competition than national terms"

### Strategic Insights

- "Competitor weakness identified: YogaJournal.com has limited beginner-focused content"
- "Content gap: No comprehensive guides for 'yoga equipment for home practice'"
- "Seasonal opportunity: 'new year yoga challenge' peaks in January with lower competition"

This framework ensures the keyword research agent provides actionable, strategic recommendations tailored to the client's domain authority and business goals.
