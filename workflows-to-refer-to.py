import json
import logging
import datetime
import numpy as np
from typing import List, Dict, Any, Optional

# --- Configuration & Setup ---
# Setup basic logging for auditability
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 1. The Agent's Tools Library (tools.py) ---



class PromptBuilderTool:
    """
    Responsible for constructing the final prompt sent to the LLM,
    combining instructions with context from the Vector DB.
    """
    def build_article_prompt(self, cluster_data: Dict[str, Any], similar_articles: List[Dict[str, Any]], 
                             site_profile: Optional[List[Dict[str, Any]]] = None, 
                             task_history: Optional[List[Dict[str, Any]]] = None) -> str:
        """Constructs a detailed, context-rich prompt string for content generation."""
        logging.info("TOOL: PromptBuilderTool - Constructing prompt with retrieved context...")

        prompt = """
You are an expert SEO content writer and strategist. Your task is to generate a comprehensive, helpful, and engaging article based on the provided details. Adhere strictly to the requested format and tone. Your output must be in Markdown.
"""
        # Add Site Profile Context
        if site_profile and site_profile[0]['metadata'].get('doc_type') == 'site_profile':
            prompt += f"\n--- BRAND/SITE CONTEXT ---\n{site_profile[0]['metadata']['content']}\n---\n"
            
        prompt += f"\n--- ARTICLE REQUIREMENTS ---\n"
        prompt += f"\n**Primary Topic:** {cluster_data['name']}"
        prompt += f"\n**Primary Keyword:** {cluster_data['primary_keyword']}"

        # Add Context for Internal Linking
        if similar_articles:
            prompt += "\n\n--- EXISTING RELATED CONTENT (For Internal Linking & Semantic Consistency) ---\n"
            prompt += "To ensure our content is interconnected, naturally link to these existing articles where relevant. Do not force links, ensure anchors are descriptive.\n"
            for article in similar_articles:
                meta = article['metadata']
                prompt += f"- **Existing Article:** '{meta['title']}' (URL: {meta['url']}). This article covers the topic of '{meta['cluster']}'.\n"
            prompt += "---\n"
        
        # Add Task History Context
        if task_history:
            prompt += "\n\n--- RECENT TASK HISTORY (For Contextual Awareness) ---\n"
            prompt += "Be aware of these recent actions/learnings:\n"
            for task in task_history:
                meta = task['metadata']
                prompt += f"- Task on {meta.get('date')}: {meta.get('content_summary', 'No summary provided')}\n"
            prompt += "---\n"

        prompt += "\nPlease write the full article now, ensuring it meets all specified requirements and integrates contextual information naturally."
        return prompt

# class PromptBuilderTool:
#     def build_article_prompt(self, cluster_data: Dict[str, Any], similar_articles: List[Dict[str, Any]]) -> str:
#         logging.info("TOOL: PromptBuilderTool - Constructing prompt with retrieved context...")
#         prompt = f"You are an expert SEO content writer. Write a comprehensive article about '{cluster_data['name']}'. The primary keyword is '{cluster_data['primary_keyword']}'.\n"
#         if similar_articles:
#             prompt += "\nContext from existing articles to link to:\n"
#             for article in similar_articles:
#                 meta = article['metadata']
#                 prompt += f"- Link to '{meta['title']}' ({meta['url']}) when relevant.\n"
#         return prompt

# # end build_article_prompt



class ActionableInsightsTool:
    def generate_optimization_plan(self, client_url: str, target_keyword: str, content_gap: Dict, backlink_gap: Dict) -> str:
        logging.info("TOOL: ActionableInsightsTool - Generating optimization plan...")
        plan = f"--- ACTION PLAN FOR: {client_url} | TARGET KEYWORD: {target_keyword} ---\n"
        plan += "\n**1. Content Enhancement (Immediate Action):**\n   Update the page to include sections on: " + ", ".join(content_gap.get("missing_topics", [])) + ".\n"
        plan += "\n**2. Backlink Acquisition (Ongoing):**\n"
        for opportunity in backlink_gap.get("high_da_opportunities", []):
            plan += f"   - Target: {opportunity['linking_from']} (DA: {opportunity['domain_authority']}).\n"
        return plan
      # end generate_optimization_plan
      # end ActionableInsightsTool
      


class ContentGenerationTool:
    
    """A tool for generating content using an LLM."""
    def generate_article_from_brief(self, brief: Dict[str, Any]) -> Dict[str, str]:
        """
        Takes a content brief and returns a finalized article draft.
        This function wraps the `ContentGenerationAPI` primitive.
        """
        logging.info(f"TOOL: ContentGenerationTool - Generating article for cluster: {brief['content_variables']['target_cluster_name']}")
        
        # In a real implementation, this would:
        # 1. Format the 'brief' into the `ContentRequest` structure.
        # 2. Make an HTTP POST request to the LLM API endpoint.
        # 3. Handle the response, checking for errors.
        # 4. Return the parsed `ContentResponse`.

        # Mock LLM Response
        return {
            "title": f"A Guide to {brief['content_variables']['target_cluster_name']}",
            "meta_description": f"Everything you need to know about {brief['content_variables']['target_cluster_name']}.",
            "body_markdown": f"# Generated Article Title\n\nThis is the AI-generated content based on the rich context provided.\n\nHere's a natural internal link: For more details on related topics, you can check out our existing guide on [Internal Link Title](https://yourdomain.com/blog/some-slug)."
        }

        
    def generate_article_from_prompt(self, prompt: str) -> Dict[str, str]:
        logging.info(f"TOOL: ContentGenerationTool - Sending final prompt to LLM...")
        return {"title": "Generated Title", "meta_description": "Generated Desc", "body_markdown": "# Generated Content\nThis is a mock article."}


class PerformanceMonitoringTool:
    """A tool for fetching SEO performance data."""
    def get_performance_data(self, tracked_urls: List[str]) -> Dict[str, Any]:
        """
        Fetches performance data (clicks, impressions, position) for a list of URLs.
        """
        logging.info(f"TOOL: PerformanceMonitoringTool - Fetching data for {len(tracked_urls)} URLs...")
        # Real implementation would query Google Search Console API.
        # Mocking data that shows positive trends.
        performance_data = {}
        for url in tracked_urls:
            performance_data[url] = {
                "clicks": 50,
                "impressions": 1000,
                "position": 15.0 # Simulating an average position
            }
        return performance_data


class CmsTool:
    def publish_article(self, article_data: Dict[str, str], url_slug: str) -> str:
        live_url = f"https://yourdomain.com/blog/{url_slug}"
        logging.info(f"TOOL: CmsTool - Article published at {live_url}")
        return live_url
# end publish_article


class VectorizationService:
    """A simple service to convert text into numerical vectors."""
    def __init__(self):
        self.vocab = {
            "seo": [0.1, 0.2, 0.3], "content": [0.2, 0.3, 0.4], "ev": [0.9, 0.1, 0.1],
            "charger": [0.8, 0.2, 0.1], "cost": [0.4, 0.5, 0.6], "install": [0.7, 0.3, 0.2],
            "planning": [0.3, 0.6, 0.8], "road": [0.5, 0.5, 0.1], "trip": [0.6, 0.7, 0.9]
        }
        self.vector_size = 3

    def vectorize(self, text: str) -> List[float]:
        words = text.lower().split()
        vectors = [self.vocab.get(word, np.zeros(self.vector_size)) for word in words]
        if not vectors: return list(np.zeros(self.vector_size))
        return list(np.mean(vectors, axis=0))

class VectorDBTool:
    """A tool for managing and searching a vector database for semantic context."""
    def __init__(self, db_path='vector_db.json'):
        self.db_path = db_path
        self.vectorizer = VectorizationService()
        self.index = self._load_index()

    def _load_index(self):
        try:
            with open(self.db_path, 'r') as f:
                logging.info(f"VECTOR DB: Index loaded from {self.db_path}")
                return json.load(f)
        except FileNotFoundError:
            logging.info("VECTOR DB: No index file found. Initializing new index.")
            return []
    
    def _save_index(self):
        with open(self.db_path, 'w') as f: json.dump(self.index, f, indent=2)
    
    
    @staticmethod
    def _cosine_similarity(v1, v2):
        v1, v2 = np.array(v1), np.array(v2)
        dot = np.dot(v1, v2)
        norm = np.linalg.norm(v1) * np.linalg.norm(v2)
        return dot / norm if norm != 0 else 0.0
    
    def add(self, text_content: str, metadata: Dict[str, Any]):
        vector = self.vectorizer.vectorize(text_content)
        self.index.append({"vector": vector, "metadata": metadata})
        self._save_index()
    
    # def search(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
    #     query_vector = self.vectorizer.vectorize(query_text)
    #     if not self.index: return []
    #     results = [{"similarity": self._cosine_similarity(query_vector, item['vector']), "metadata": item['metadata']} for item in self.index]
    #     results.sort(key=lambda x: x['similarity'], reverse=True)
    #     return results[:top_k]
    
    def search(self, query_text: str, doc_type: Optional[str] = None, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Finds the most semantically similar items in the index.
        Can filter by 'doc_type' for targeted searches.
        """
        logging.info(f"VECTOR DB: Searching for content similar to '{query_text[:50]}...' (Type: {doc_type or 'Any'})")
        query_vector = self.vectorizer.vectorize(query_text)
        
        if not self.index:
            return []

        filtered_index = self.index
        if doc_type:
            filtered_index = [item for item in self.index if item['metadata'].get('doc_type') == doc_type]

        results = []
        for item in filtered_index:
            similarity = self._cosine_similarity(query_vector, item['vector'])
            results.append({"similarity": similarity, "metadata": item['metadata']})

        # Sort by similarity and return top_k
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]

class KeywordResearchTool:
    # In a real implementation, this would involve a multi-step process:
        # 1. Call keyword expansion APIs.
        # 2. Call SERP analysis APIs for each keyword.
        # 3. Run the clustering algorithm.
        # 4. Enrich clusters with volume/KD/CPC data.
        # Here we return a mock, enriched response.
    
    def find_keyword_clusters(self, location: str = "US") -> List[Dict[str, Any]]:
        logging.info(f"TOOL: KeywordResearchTool - Finding initial clusters for location: {location}")
        return [{"name": "Home EV Charger Installation", "primary_keyword": "home ev charging installation", "metrics": {"aggregate_volume": 12500, "average_kd": 35, "average_cpc": 15.50}}]
    
    def find_top_pages_for_keyword(self, topN: int, location: str ="US") -> List[Dict[str, Any]]:
        return []
    
    def get_keywords_for_url(self, url: str) -> List[Dict[str, Any]]:
        logging.info(f"TOOL: KeywordResearchTool - Getting ranking keywords for URL: {url}")
        if "client-website.com/ev-chargers" in url:
            return [{"keyword": "best level 2 chargers", "position": 8, "volume": 8000}]
        return []
    
    def find_shoulder_keyword_clusters(self, existing_topics: List[str]) -> List[Dict[str, Any]]:
        """Finds clusters for related, non-direct 'shoulder' topics."""
        logging.info(f"TOOL: KeywordResearchTool - Finding shoulder clusters, avoiding existing topics...")
        return [
            {"name": "EV Road Trip Planning", "primary_keyword": "ev road trip planning", "secondary_keywords": [], "user_intent": "Informational", "metrics": {"aggregate_volume": 9500, "average_kd": 25, "average_cpc": 1.50}}
        ]


# Can (And should) Parameterize this by strategy, apis etc for entreprise clients
class CompetitorAnalysisTool:
    def find_top_competitors(self, keyword: str, count: int = 3) -> List[Dict[str, Any]]:
        logging.info(f"TOOL: CompetitorAnalysisTool - Finding top {count} competitors for '{keyword}'")
        return [{"url": "https://competitor1.com/best-chargers", "position": 1}]
    def get_content_gap(self, client_url: str, competitor_urls: List[str]) -> Dict[str, List[str]]:
        logging.info("TOOL: CompetitorAnalysisTool - Analyzing content gap...")
        return {"missing_topics": ["Smart charging features", "Warranty information"]}
    def get_backlink_gap(self, client_url: str, competitor_urls: List[str]) -> Dict[str, List[Dict]]:
        logging.info("TOOL: CompetitorAnalysisTool - Analyzing backlink gap...")
        return {"high_da_opportunities": [{"linking_from": "https://tech-review-site.com/best-ev-gear", "domain_authority": 75}]}

class BacklinkAcquisitionTool:
    """
    NEW TOOL: Finds backlink opportunities using various strategies.
    """
    def find_guest_post_opportunities(self, keyword: str, strategy: str = "google_dorks") -> List[Dict[str, Any]]:
        """
        Finds guest posting opportunities. The default strategy simulates using
        Google dorks like 'keyword "write for us"' or 'keyword "guest post"'.
        """
        logging.info(f"TOOL: BacklinkAcquisitionTool - Searching for guest post opportunities for '{keyword}' using strategy: {strategy}")
        # In a real implementation:
        # 1. Construct Google dorks (e.g., f'"{keyword}" "write for us"').
        # 2. Use a search API (e.g., Google Custom Search API or a SERP API) to get results.
        # 3. For each result URL, use a backlink API (e.g., Ahrefs, Moz, Semrush) to get DR/DA, estimated traffic.
        # 4. Filter/sort by desired criteria.

        return [
            {"target_url": "https://energycentral.com/write-for-us", "domain_authority": 78, "estimated_traffic": 150000},
            {"target_url": "https://ev-enthusiast-blog.com/guest-posts", "domain_authority": 55, "estimated_traffic": 45000},
        ]

class TechnicalSEOTool:
    """
    NEW TOOL: Audits a URL for technical SEO issues against requirements.
    """
    def audit_url(self, url: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        logging.info(f"TOOL: TechnicalSEOTool - Auditing {url}")
        # Simulate crawling the URL and checking its technical aspects.
        # This would use libraries like `requests` and `BeautifulSoup`.
       # Simulate crawling the URL and checking its technical aspects.
        # This would use libraries like `requests`, `BeautifulSoup`, and potentially Lighthouse API.
        mock_actual_state = {
            "load_speed_lcp": 3.1, # Example: Fails the < 2.5s target
            "is_mobile_friendly": True, # Example: Passes
            "has_schema": True, # Example: Passes
            "schema_type": "Service", # Example: Passes
            "canonical_url": url, # Example: Passes (self-referencing)
            "robots_txt_status": "Accessible",
            "sitemap_status": "Found and Parsable",
            "broken_links_found": 0,
            "redirects_found": 0
        }
        
        results = {
            "load_speed_check": "FAIL" if mock_actual_state["load_speed_lcp"] > requirements["load_speed_target"] else "PASS",
            "mobile_friendly_check": "PASS" if mock_actual_state["is_mobile_friendly"] == requirements["is_mobile_friendly"] else "FAIL",
            "schema_check": "PASS" if mock_actual_state["has_schema"] and mock_actual_state["schema_type"] == requirements["schema_markup"]["type"] else "FAIL",
            "robots_txt_check": "PASS" if mock_actual_state["robots_txt_status"] == "Accessible" else "FAIL",
            "sitemap_check": "PASS" if mock_actual_state["sitemap_status"] == "Found and Parsable" else "FAIL",
            "broken_links_check": "PASS" if mock_actual_state["broken_links_found"] == 0 else f"FAIL ({mock_actual_state['broken_links_found']} broken links found)",
            "redirect_chains_check": "PASS" if mock_actual_state["redirects_found"] == 0 else f"FAIL ({mock_actual_state['redirects_found']} redirect chains found)"
        }
        
        return results

class EEATAnalysisTool:
    """
    NEW TOOL: Audits a URL for E-E-A-T signals against requirements.
    """
    def audit_url(self, url: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        logging.info(f"TOOL: EEATAnalysisTool - Auditing {url}")
        # This would scrape the page and use NLP/heuristics to check for signals.
         # This would scrape the page content and check for indicators:
        # - Author bio presence and linking to credentials
        # - Citations/references to authoritative sources
        # - Testimonials/reviews on page
        # - Contact info, privacy policy (trust signals)
        # - Dates of content last updated (freshness)
        mock_signals_found = {
            "author_bio_found": True, 
            "author_credentials_linked": True,
            "cited_sources_detected": False, # Example: Fails
            "testimonials_found": True,
            "contact_info_present": True,
            "privacy_policy_linked": True,
            "last_updated_date_present": True
        }
        
        results = {
            "author_bio_check": "PASS" if mock_signals_found["author_bio_found"] == requirements["author_bio_required"] else "FAIL",
            "author_credentials_check": "PASS" if mock_signals_found["author_credentials_linked"] else "FAIL (Author credentials not clearly linked)",
            "cited_sources_check": "PASS" if mock_signals_found["cited_sources_detected"] else "FAIL (No outbound links to authoritative sources detected)",
            "testimonials_check": "PASS" if mock_signals_found["testimonials_found"] == requirements["include_testimonials"] else "FAIL",
            "trust_signals_check": "PASS" if mock_signals_found["contact_info_present"] and mock_signals_found["privacy_policy_linked"] else "FAIL (Missing key trust signals)",
            "freshness_check": "PASS" if mock_signals_found["last_updated_date_present"] else "FAIL (No 'last updated' date found)"
        }
        return results

class OnPageSEOAnalysisTool:
    """

    NEW TOOL: Audits a URL for On-Page SEO targets and provides recommendations.
    """
    def audit_url(self, url: str, targets: Dict[str, Any]) -> Dict[str, Any]:
        logging.info(f"TOOL: OnPageSEOAnalysisTool - Auditing {url}")
        # This would scrape the page to get the current on-page elements.
        # It would also use NLP to analyze content depth, keyword usage, etc.
        mock_on_page_state = {
            "title_tag": "EV Charger Installation Guide", # Too short, missing brand
            "meta_description": "Learn about EV chargers.", # Too generic
            "h1": "Charger Installation Basics", # Doesn't match primary keyword exactly
            "content_word_count": 800, # Example
            "keyword_density_primary": 0.5, # Example
            "internal_links_count": 10
        }
        recommendations = []
        # Title Tag Check
        if targets["primary_keyword"] not in mock_on_page_state["title_tag"].lower() or len(mock_on_page_state["title_tag"]) < 30:
            recommendations.append(f"Title tag is suboptimal. Recommended: '{targets.get('title_tag', 'Improve with primary keyword and brand')}'")
        
        # Meta Description Check
        if len(mock_on_page_state["meta_description"]) < 50 or targets["primary_keyword"] not in mock_on_page_state["meta_description"].lower():
            recommendations.append(f"Meta description is too short or lacks primary keyword. Recommended: '{targets.get('meta_description', 'Improve with keyword and strong CTA')}'")

        # H1 Check
        if mock_on_page_state["h1"].lower() != targets["h1"].lower():
            recommendations.append(f"H1 does not match target. Recommended: '{targets['h1']}'")
        
        # Content Depth (Example: assuming target is >1500 words for this cluster)
        if mock_on_page_state["content_word_count"] < 1500:
             recommendations.append(f"Content appears thin ({mock_on_page_state['content_word_count']} words). Consider expanding to at least 1500 words for comprehensive coverage.")

        # Internal Linking opportunities (Conceptual)
        # This would use the VectorDBTool to find under-linked relevant pages on the site.
        # For this example, we'll just check if a certain number of links exist.
        if mock_on_page_state["internal_links_count"] < 5:
            recommendations.append("Page has too few internal links. Search VectorDB for relevant existing content to link from.")

        return {"recommendations": recommendations}

# --- 2. The Agent's State Management ---
class WorkflowState:
    def __init__(self, state_file_path: str = 'workflow_state.json'):
        self.state_file_path = state_file_path; 
        self.data = self._load_state()
        
    def _load_state(self):
        try:
            with open(self.state_file_path, 'r') as f:
                logging.info(f"STATE: State loaded from {self.state_file_path}")
                return json.load(f)
        
        except FileNotFoundError: 
             logging.info("STATE: No state file found. Initializing new state.")
             
             return {
                "content_velocity_per_week": 3,
                "prioritized_backlog": [],
                "published_assets": {}, # {url: {publish_date, cluster_name, content_summary}}
                "performance_history": {} # {date: {performance_data_summary}}
            }
        
        except json.JSONDecodeError:
            logging.error(f"STATE: Error decoding JSON from {self.state_file_path}. Initializing new state.")
            return {
                "content_velocity_per_week": 3,
                "prioritized_backlog": [],
                "published_assets": {}, 
                "performance_history": {}
            }
            
    def save_state(self):
        with open(self.state_file_path, 'w') as f: 
            json.dump(self.data, f, indent=2)
            
    def get(self, key: str): 
        return self.data.get(key)
    
    def set(self, key: str, value: Any): 
        self.data[key] = value; self.save_state()
        
    def add_published_asset(self, url: str, cluster_name: str, content: str):
        self.data['published_assets'][url] = {
            "publish_date": datetime.date.today().isoformat(), 
            "cluster_name": cluster_name, 
            "content_summary": content[:200]}; # Store a summary for context retrieval
        self.save_state()
    
    def get_all_published_urls(self) -> List[str]:
        return list(self.data['published_assets'].keys())

    def add_performance_data_summary(self, date: str, summary: Dict[str, Any]):
        self.data['performance_history'][date] = summary
        self.save_state()

        
# --- 3. The High-Level Workflow API (Updated with New Workflow) ---
class AutonomousSEOWorkflow:
    """The main orchestrator for the autonomous SEO agent."""
    def __init__(self):
        self.state = WorkflowState()
        self.tools = {
            "research": KeywordResearchTool(),
            "backlinks": BacklinkAcquisitionTool(), # NEW
            "technical_seo": TechnicalSEOTool(), # NEW
            "eeat_analysis": EEATAnalysisTool(), # NEW
            "on_page_seo": OnPageSEOAnalysisTool(), # NEW
            "insights": ActionableInsightsTool(),
            "vector_db": VectorDBTool(), 
            "content": ContentGenerationTool(), # Other tools are not used in this new workflow
            "prompt_builder": PromptBuilderTool(), 
            "cms": CmsTool(),
            "competitor_analysis": CompetitorAnalysisTool(),
            "performace": PerformanceMonitoringTool(),
        }
    
    # def run_initialization(self):
    #     # if not self.state.get("prioritized_backlog"):
    #     #     logging.info("WORKFLOW: Backlog empty. Initializing...")
    #     #     clusters = self.tools["research"].find_keyword_clusters()
    #     #     self.state.set("prioritized_backlog", clusters)
    #     if not self.state.get("prioritized_backlog"):
    #         logging.info("WORKFLOW: Backlog is empty. Running initialization...")
    #         clusters = self.tools["research"].find_keyword_clusters()
    #         # This implements the prioritization logic from the Planner module
    #         clusters.sort(
    #             key=lambda c: (c['metrics']['aggregate_volume'] * c['metrics']['average_cpc']) / c['metrics']['average_kd'],
    #             reverse=True
    #         )
    #         self.state.set("prioritized_backlog", clusters)
    #         logging.info(f"WORKFLOW: Initialization complete. {len(clusters)} clusters prioritized.")


    
    def run_initialization(self):
        """Initializes the workflow by fetching initial keyword clusters if the backlog is empty."""
        if not self.state.get("prioritized_backlog"):
            logging.info("WORKFLOW: Backlog is empty. Running initialization...")
            clusters = self.tools["research"].find_keyword_clusters(location="KE") # Example location
            # Simple prioritization by combined metric (volume * cpc / kd)
            clusters.sort(
                key=lambda c: (c['metrics']['aggregate_volume'] * c['metrics']['average_cpc']) / c['metrics']['average_kd'],
                reverse=True
            )
            self.state.set("prioritized_backlog", clusters)
            logging.info(f"WORKFLOW: Initialization complete. {len(clusters)} clusters prioritized.")

            # Initial site profile context (example of storing other types of context)
            site_profile_text = (
                "Site Profile for YourBrand.com: The company operates in the electric vehicle (EV) "
                "home energy sector. Its primary products are Level 2 home charging stations. "
                "The target audience is new EV owners in Kenya looking for reliable and affordable "
                "home charging solutions. Key competitors include LocalEVCharge, GreenHomePower. "
                "The brand's tone of voice is helpful, expert, and slightly tech-focused but "
                "accessible to a non-electrician audience."
            )
            self.tools["vector_db"].add(site_profile_text, {"doc_type": "site_profile", "client_domain": "yourbrand.com", "content": site_profile_text})


    # # Create the content brief object needed by the ContentGenerationTool
    #         content_brief = {
    #             "model": "gpt-4.1-turbo",
    #             "temperature": 0.7,
    #             "author_persona": "An expert in the target field.",
    #             "system_prompt": "You are an expert SEO content writer...",
    #             "content_variables": {
    #                 "target_cluster_name": target_cluster['name'],
    #                 "primary_keyword": target_cluster['primary_keyword'],
    #                 # ... and other details from the cluster
    #             }
    #         }
            
    
    # def execute_daily_task(self):
    #     """
    #     The main entry point, run daily by a scheduler (like a cron job).
    #     """
    #     logging.info("==============================================")
    #     logging.info("WORKFLOW: Starting daily execution cycle...")
    #     self.run_initialization()

    #     # --- Scheduler Logic ---
    #     velocity = self.state.get("content_velocity_per_week")
    #     # Simple logic: spread N tasks over 5 working days
    #     if datetime.date.today().weekday() < velocity:
    #         backlog = self.state.get("prioritized_backlog")
    #         if not backlog:
    #             logging.warning("WORKFLOW: Content backlog is empty. Cannot publish.")
    #             return

    #         # --- Brief Generator & Content Generator Logic ---
    #         target_cluster = backlog.pop(0) # Dequeue the next task
    #         logging.info(f"WORKFLOW: Task dequeued. Preparing to generate content for: {target_cluster['name']}")

    #         # Create the content brief object needed by the ContentGenerationTool
    #         content_brief = {
    #             "model": "gpt-4.1-turbo",
    #             "temperature": 0.7,
    #             "author_persona": "An expert in the target field.",
    #             "system_prompt": "You are an expert SEO content writer...",
    #             "content_variables": {
    #                 "target_cluster_name": target_cluster['name'],
    #                 "primary_keyword": target_cluster['primary_keyword'],
    #                 # ... and other details from the cluster
    #             }
    #         }
            
    #         article_draft = self.tools["content"].generate_article_from_brief(content_brief)

    #         # --- Publisher Logic ---
    #         # Create a URL-friendly slug from the primary keyword
    #         url_slug = target_cluster['primary_keyword'].replace(" ", "-")
    #         live_url = self.tools["cms"].publish_article(article_draft, url_slug)
            
    #         # Update state with the new asset
    #         self.state.add_published_asset(live_url, target_cluster['name'])
    #         self.state.set("prioritized_backlog", backlog) # Save the updated backlog

    #         logging.info("WORKFLOW: Daily task completed successfully.")
    #     else:
    #         logging.info("WORKFLOW: No task scheduled for today based on content velocity.")
    #     logging.info("==============================================")

    def execute_daily_task(self):
        """
        The main entry point for daily content creation tasks.
        This method would be run daily by an external scheduler (like a cron job).
        """
        logging.info("="*50)
        logging.info("WORKFLOW: Starting daily NEW CONTENT creation cycle...")
        self.run_initialization() # Ensure backlog is populated

        # --- Scheduler Logic: Determines if a content piece should be published today ---
        velocity = self.state.get("content_velocity_per_week")
        # Simple logic: allows publishing on weekdays up to the velocity count.
        # e.g., if velocity=3, publishes Mon, Tue, Wed.
        if datetime.date.today().weekday() < velocity:
            backlog = self.state.get("prioritized_backlog")
            if not backlog:
                logging.warning("WORKFLOW: Content backlog is empty. No new content to publish today.")
                return

            target_cluster = backlog.pop(0) # Dequeue the next prioritized cluster
            logging.info(f"WORKFLOW: Task dequeued. Preparing to generate content for: {target_cluster['name']}")

            # --- Contextual Prompt Building ---
            # 1. Retrieve relevant existing articles for internal linking
            query_for_articles = f"{target_cluster['primary_keyword']} {target_cluster['name']}"
            similar_articles = self.tools["vector_db"].search(query_for_articles, doc_type="published_article", top_k=3)
            if similar_articles:
                logging.info(f"WORKFLOW: Found {len(similar_articles)} semantically similar existing articles for internal linking.")
            
            # 2. Retrieve the site profile for brand tone and overall context
            site_profile_results = self.tools["vector_db"].search("site profile", doc_type="site_profile", top_k=1)
            site_profile_context = site_profile_results[0] if site_profile_results else None

            # 3. Retrieve recent task logs (e.g., to inform writing style based on past successes/failures)
            recent_task_logs = self.tools["vector_db"].search("recent task logs", doc_type="task_log", top_k=2)

            # Build the comprehensive prompt
            final_prompt = self.tools["prompt_builder"].build_article_prompt(
                cluster_data=target_cluster,
                similar_articles=similar_articles,
                site_profile=site_profile_context,
                task_history=recent_task_logs
            )
            
            # --- Content Generation ---
            article_draft = self.tools["content_gen"].generate_article_from_prompt(final_prompt)

            # --- Publishing ---
            url_slug = target_cluster['primary_keyword'].replace(" ", "-").lower() # Ensure URL-friendly
            live_url = self.tools["cms"].publish_article(article_draft, url_slug)
            
            # --- Update Agent's Memory (Vector DB and Workflow State) ---
            new_asset_metadata = {
                "doc_type": "published_article", # Explicitly mark this type of document
                "url": live_url, 
                "title": article_draft['title'], 
                "cluster": target_cluster['name'],
                "publish_date": datetime.date.today().isoformat()
            }
            self.tools["vector_db"].add(article_draft['body_markdown'], new_asset_metadata) # Add content to Vector DB
            self.state.add_published_asset(live_url, target_cluster['name'], article_draft['body_markdown']) # Update state
            self.state.set("prioritized_backlog", backlog) # Save the updated backlog (removed dequeued item)

            logging.info("WORKFLOW: Daily new content task completed successfully.")
        else:
            logging.info("WORKFLOW: No new content scheduled for today based on content velocity.")
        logging.info("="*50)


    # def execute_daily_task(self):
        
        
    #     logging.info("="*50 + "\nWORKFLOW: Starting daily NEW CONTENT creation cycle...")
    #     self.run_initialization()
    #     velocity = self.state.get("content_velocity_per_week")
    #     if velocity is not None and isinstance(velocity, int) and datetime.date.today().weekday() < velocity:
    #         backlog = self.state.get("prioritized_backlog")
    #         if not backlog: logging.warning("WORKFLOW: Content backlog empty."); return
    #         target_cluster = backlog.pop(0)
    #         logging.info(f"WORKFLOW: Task dequeued for: {target_cluster['name']}")
    #         similar_assets = self.tools["vector_db"].search(target_cluster['primary_keyword'], top_k=2)
    #         final_prompt = self.tools["prompt_builder"].build_article_prompt(target_cluster, similar_assets)
    #         article_draft = self.tools["content"].generate_article_from_prompt(final_prompt)
    #         url_slug = target_cluster['primary_keyword'].replace(" ", "-")
    #         live_url = self.tools["cms"].publish_article(article_draft, url_slug)
    #         new_asset_metadata = {"url": live_url, "title": article_draft['title'], "cluster": target_cluster['name']}
    #         self.tools["vector_db"].add(article_draft['body_markdown'], new_asset_metadata)
    #         self.state.add_published_asset(live_url, target_cluster['name'], article_draft['body_markdown'])
    #         self.state.set("prioritized_backlog", backlog)
    #         logging.info("WORKFLOW: Daily new content task completed.")
    #     else: logging.info("WORKFLOW: No new content scheduled for today.")
    def run_strategic_review(self):
        """
        Implements the Planner module's logic for strategic pivots.
        This would be run on a broader schedule (e.g., monthly/quarterly).
        """
        logging.info("WORKFLOW: Running strategic review...")
        # Placeholder for more complex logic:
        # 1. Fetch recent performance data for all published assets from PerformanceMonitoringTool.
        # 2. Analyze trends (e.g., average position change, traffic growth).
        # 3. Adjust content velocity based on performance.
        #    e.g., if avg_pos improves > X% over last 3 months, increase velocity.
        # 4. Check backlog status:
        #    If primary backlog is low, trigger `find_shoulder_keyword_clusters` from KeywordResearchTool
        #    and add them to the backlog with appropriate prioritization.
        
        # Example of storing a task log after a review
        review_summary = {
            "date": datetime.date.today().isoformat(),
            "action": "strategic_review",
            "outcome": "Simulated velocity adjustment or backlog re-prioritization."
        }
        self.tools["vector_db"].add(json.dumps(review_summary), {"doc_type": "task_log", "date": review_summary['date'], "content_summary": "Strategic review completed."})


    # def run_strategic_review(self):
    #     """
    #     Implements the Planner module's logic for strategic pivots.
    #     This would be run on a schedule (e.g., quarterly).
    #     """
    #     logging.info("WORKFLOW: Running quarterly strategic review...")
    #     # In a real agent, this method would contain the logic for
    #     # performance acceleration and shoulder keyword transition.
    #     # For brevity, we are focusing on the main execution loop here.
    #     # Example logic: check performance_history, adjust content_velocity_per_week
    #     pass

    
    def run_content_optimization_analysis(self, client_url: str):
        """
        Runs a full competitor gap analysis and generates an
        actionable optimization plan for a given URL.
        """
        logging.info("="*50)
        logging.info(f"OPTIMIZATION WORKFLOW: Starting analysis for URL: {client_url}")

        # Step 1: Analyze the client's URL to find a target keyword
        ranking_keywords = self.tools["research"].get_keywords_for_url(client_url)
        if not ranking_keywords:
            logging.error(f"OPTIMIZATION WORKFLOW: Could not find any ranking keywords for {client_url}. Aborting.")
            return

        # Find the highest volume keyword where we are not in the top 5 (striking distance)
        ranking_keywords.sort(key=lambda x: x['volume'], reverse=True)
        target_keyword_data = next((kw for kw in ranking_keywords if kw['position'] > 5), None)

        if not target_keyword_data:
            logging.warning(f"OPTIMIZATION WORKFLOW: No suitable 'striking distance' keywords found for {client_url}. The page may already be well-optimized or too low ranking.")
            return
        
        target_keyword = target_keyword_data['keyword']
        logging.info(f"OPTIMIZATION WORKFLOW: Identified target keyword: '{target_keyword}' (Current Position: {target_keyword_data['position']})")

        # Step 2: Get competitor data for the target keyword
        competitors = self.tools["competitor_analysis"].find_top_competitors(target_keyword)
        competitor_urls = [c['url'] for c in competitors]
        logging.info(f"OPTIMIZATION WORKFLOW: Found {len(competitors)} top competitors for '{target_keyword}'.")

        # Step 3: Run Gap Analyses (Content, Backlink)
        content_gap = self.tools["competitor_analysis"].get_content_gap(client_url, competitor_urls)
        backlink_gap = self.tools["competitor_analysis"].get_backlink_gap(client_url, competitor_urls)
        logging.info("OPTIMIZATION WORKFLOW: Content and Backlink gap analysis completed.")

        # Step 4: Run On-Page, Technical, and E-E-A-T Audits for the client URL
        # Define the "Ideal State" based on a generic understanding or pre-configured values for this domain/industry
        # In a real system, these 'targets' could come from a pre-defined site config or be inferred from best practices.
        on_page_targets_example = {
            "primary_keyword": target_keyword,
            "title_tag": f"{target_keyword.title()} | Best {target_keyword.title()}s - ClientBrand",
            "h1": target_keyword.title(),
            "meta_description": f"Find the {target_keyword} and get all your questions answered with this comprehensive guide from ClientBrand.",
        }
        technical_requirements_example = {
            "load_speed_target": 2.5, # seconds LCP
            "is_mobile_friendly": True,
            "schema_markup": {"type": "Product" if "best" in target_keyword else "Article"}
        }
        eeat_signals_example = {
            "author_bio_required": True,
            "include_testimonials": True,
            "cite_sources": True # Requirement to cite sources
        }

        on_page_report = self.tools["on_page_seo"].audit_url(client_url, on_page_targets_example)
        technical_report = self.tools["technical_seo"].audit_url(client_url, technical_requirements_example)
        eeat_report = self.tools["eeat_analysis"].audit_url(client_url, eeat_signals_example)
        logging.info("OPTIMIZATION WORKFLOW: On-Page, Technical, and E-E-A-T audits completed.")

        # Step 5: Find Backlink Acquisition Opportunities (Guest Posting specific for now)
        guest_post_opportunities = self.tools["backlink_acq"].find_guest_post_opportunities(target_keyword, strategy="google_dorks")
        logging.info(f"OPTIMIZATION WORKFLOW: Found {len(guest_post_opportunities)} guest post opportunities.")

        # Step 6: Generate Actionable Plan combining all findings
        # This part should be enriched to include technical & EEAT insights
        action_plan = self.tools["insights"].generate_optimization_plan(
            client_url=client_url, 
            target_keyword=target_keyword, 
            content_gap=content_gap, 
            backlink_gap=backlink_gap,
            on_page_report=on_page_report, # Pass audit reports
            technical_report=technical_report,
            eeat_report=eeat_report,
            guest_post_opportunities=guest_post_opportunities # Pass guest post opps
        )

        logging.info("\n--- Generated Comprehensive Optimization Action Plan ---\n")
        print(action_plan)
        logging.info("OPTIMIZATION WORKFLOW: Analysis complete.")
        logging.info("="*50)

    
    # def run_content_optimization_analysis(self, client_url: str):
    #     logging.info("="*50 + f"\nOPTIMIZATION WORKFLOW: Starting analysis for URL: {client_url}")
    #     ranking_keywords = self.tools["research"].get_keywords_for_url(client_url)
    #     if not ranking_keywords: logging.error(f"No ranking keywords for {client_url}. Aborting."); return
    #     target_keyword_data = next((kw for kw in ranking_keywords if kw['position'] > 5), None)
    #     if not target_keyword_data: logging.warning(f"No 'striking distance' keywords for {client_url}."); return
    #     target_keyword = target_keyword_data['keyword']
    #     logging.info(f"Identified target keyword: '{target_keyword}' (Position: {target_keyword_data['position']})")
    #     competitors = self.tools["competitor_analysis"].find_top_competitors(target_keyword)
    #     competitor_urls = [c['url'] for c in competitors]
    #     content_gap = self.tools["competitor_analysis"].get_content_gap(client_url, competitor_urls)
    #     backlink_gap = self.tools["competitor_analysis"].get_backlink_gap(client_url, competitor_urls)
    #     action_plan = self.tools["insights"].generate_optimization_plan(client_url, target_keyword, content_gap, backlink_gap)
    #     logging.info("\n--- Generated Action Plan ---\n"); print(action_plan)
    #     logging.info("OPTIMIZATION WORKFLOW: Analysis complete.")

    
    def run_full_site_audit(self, url: str):
        """
        NEW WORKFLOW: Uses the new tools to conduct a comprehensive audit
        of a single URL based on the SEO Execution Language primitives.
        """
        logging.info("="*50 + f"\nAUDIT WORKFLOW: Starting full audit for URL: {url}")

        # Step 1: Define the "Ideal State" using primitives from seo_execution_language
        # In a real app, this would be dynamically generated based on keyword research for the URL.
        # For this example, we'll use the spec we created.
        logging.info("Defining ideal state based on SEO Execution Language...")
        on_page_targets = {
            "primary_keyword": "home ev charging installation",
            "title_tag": "Home EV Charging Installation | YourBrandName",
            "h1": "Home EV Charging Installation",
        }
        technical_requirements = {
            "load_speed_target": 2.5,
            "is_mobile_friendly": True,
            "schema_markup": {"type": "Service"}
        }
        eeat_signals = {
            "author_bio_required": True,
            "include_testimonials": True
        }

        # Step 2: Run all audits
        on_page_report = self.tools["on_page_seo"].audit_url(url, on_page_targets)
        technical_report = self.tools["technical_seo"].audit_url(url, technical_requirements)
        eeat_report = self.tools["eeat_analysis"].audit_url(url, eeat_signals)
        backlink_opportunities = self.tools["backlinks"].find_guest_post_opportunities(on_page_targets["primary_keyword"])

        # Step 3: Compile and display the final report
        logging.info("\n--- COMPREHENSIVE SEO AUDIT REPORT ---")
        print(f"\nURL Audited: {url}")
        
        print("\n**1. On-Page SEO Recommendations:**")
        if on_page_report["recommendations"]:
            for rec in on_page_report["recommendations"]:
                print(f"   - [ACTION] {rec}")
        else:
            print("   - All on-page targets appear to be met. Good job!")

        print("\n**2. Technical SEO Health:**")
        for check, result in technical_report.items():
            print(f"   - {check.replace('_', ' ').title()}: {result}")

        print("\n**3. E-E-A-T Signal Analysis:**")
        for check, result in eeat_report.items():
            print(f"   - {check.replace('_', ' ').title()}: {result}")
            
        print("\n**4. Backlink Acquisition Opportunities (Guest Posting):**")
        if backlink_opportunities:
            for opp in backlink_opportunities:
                print(f"   - [OPPORTUNITY] Target Site: {opp['target_url']} (DA: {opp['domain_authority']}, Traffic: {opp['estimated_traffic']})")
        else:
            print("   - No immediate guest post opportunities found.")
        
        logging.info("\n--- AUDIT WORKFLOW COMPLETE ---" + "="*50)


# # --- Example Usage ---
# if __name__ == "__main__":
#     # seo_agent = AutonomousSEOWorkflow()

#     # # --- To run the NEW full site audit workflow for a specific page ---
#     # page_to_audit = "https://client-website.com/ev-chargers"
#     # seo_agent.run_full_site_audit(page_to_audit)
    
#     # To simulate a continuous workflow, you would run this script daily.
#     # Each run represents one day in the agent's life.
    
#     # Create an instance of the workflow
#     seo_agent = AutonomousSEOWorkflow()

#     # Execute today's task
#     seo_agent.execute_daily_task()

#     # You could then call seo_agent.run_strategic_review() on a separate schedule.



# --- Example Usage ---
if __name__ == "__main__":
    # Ensure the state file is clean for a fresh run
    try:
        # os.remove('workflow_state.json') # Uncomment to clear state on each run
        # os.remove('vector_db.json')     # Uncomment to clear vector DB on each run
        pass # Keep commented for demonstration of state persistence
    except OSError:
        pass

    seo_agent = AutonomousSEOWorkflow()

    # --- Run the daily content CREATION workflow (simulates a daily cron job) ---
    logging.info("--- SIMULATING DAILY CONTENT CREATION FOR 3 DAYS ---")
    for _ in range(3):
        seo_agent.execute_daily_task()
        # You'd typically wait a day here (e.g., time.sleep(24*60*60))
        # For simulation, we run immediately.
        print("\n" * 2) # Add some spacing for readability between daily tasks

    # --- Run the content OPTIMIZATION workflow for a specific existing page ---
    logging.info("--- SIMULATING CONTENT OPTIMIZATION AUDIT ---")
    client_page_to_optimize = "https://client-website.com/ev-chargers"
    seo_agent.run_content_optimization_analysis(client_page_to_optimize)

    # --- Optional: Run a strategic review (simulates a monthly/quarterly task) ---
    logging.info("--- SIMULATING STRATEGIC REVIEW ---")
    seo_agent.run_strategic_review()



# 2. How the Agent Uses This Richer Context
# Now, when our PromptBuilderTool constructs a prompt, it doesn't just have to search for similar articles. It can perform multiple, targeted searches to build a much richer context.

# Here is how you would conceptually modify the execute_daily_task logic:

# Python

# # Inside execute_daily_task, before building the prompt:

# # 1. Search for similar CONTENT (as before)
# similar_content = self.tools["vector_db"].search(target_cluster['primary_keyword'])

# # 2. Search for the SITE PROFILE to remind the LLM of the core business
# site_profile_context = self.tools["vector_db"].search("site profile for yourbrand.com", top_k=1)

# # 3. Search for recent TASK LOGS to see what has been done lately
# recent_tasks_context = self.tools["vector_db"].search("recent task logs", top_k=3)

# # Now, build a prompt with ALL this context
# final_prompt = self.tools["prompt_builder"].build_article_prompt(
#     cluster_data=target_cluster,
#     similar_articles=similar_content,
#     site_profile=site_profile_context, # Pass the new context in
#     task_history=recent_tasks_context  # Pass the new context in
# )

# # ...the rest of the workflow continues
# The PromptBuilderTool would then be updated to format all this information neatly for the LLM. This way, every time the agent writes an article, it's not just aware of other articles; it's also reminded of the overall business strategy, its own recent actions, and key competitor insights. This makes it far more strategic and less repetitive.
