import pytest
from src.simulator.assistant import AssistantSimulator
from src.simulator.user import UserSimulator

@pytest.fixture
def setup_simulators():
    assistant = AssistantSimulator()
    user = UserSimulator(assistant)
    return assistant, user

def test_assistant_response(setup_simulators):
    assistant, user = setup_simulators
    user_input = "What is the capital of France?"
    expected_response = "The capital of France is Paris."
    
    assistant.set_prompt("You are a helpful assistant.")
    user.send_input(user_input)
    response = assistant.get_response()
    
    assert response == expected_response

def test_user_interaction(setup_simulators):
    assistant, user = setup_simulators
    user_input = "Tell me a joke."
    expected_response = "Why did the chicken cross the road? To get to the other side!"
    
    assistant.set_prompt("You are a humorous assistant.")
    user.send_input(user_input)
    response = assistant.get_response()
    
    assert response == expected_response