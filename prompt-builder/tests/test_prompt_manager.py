import pytest
from src.core.prompt_manager import Prompt<PERSON><PERSON>ger

@pytest.fixture
def prompt_manager():
    return PromptManager()

def test_initial_prompt(prompt_manager):
    assert prompt_manager.get_prompt() == "Default system prompt."

def test_update_prompt(prompt_manager):
    new_prompt = "Updated system prompt."
    prompt_manager.update_prompt(new_prompt)
    assert prompt_manager.get_prompt() == new_prompt

def test_inject_task_example(prompt_manager):
    task_example = "Example task."
    prompt_manager.inject_task_example(task_example)
    assert task_example in prompt_manager.get_prompt()

def test_update_non_existent_prompt(prompt_manager):
    with pytest.raises(ValueError):
        prompt_manager.update_prompt(None)