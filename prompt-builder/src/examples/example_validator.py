# filepath: /prompt-builder/prompt-builder/src/examples/example_validator.py
def validate_example(example):
    if not isinstance(example, dict):
        raise ValueError("Example must be a dictionary.")
    
    required_keys = ['task', 'input', 'output']
    for key in required_keys:
        if key not in example:
            raise ValueError(f"Missing required key: {key}")

    if not isinstance(example['task'], str) or not example['task']:
        raise ValueError("Task must be a non-empty string.")
    
    if not isinstance(example['input'], str):
        raise ValueError("Input must be a string.")
    
    if not isinstance(example['output'], str):
        raise ValueError("Output must be a string.")

    return True

def validate_example_list(example_list):
    if not isinstance(example_list, list):
        raise ValueError("Example list must be a list.")
    
    for example in example_list:
        validate_example(example)

    return True