def is_valid_prompt(prompt: str) -> bool:
    """Validate the system prompt format."""
    return isinstance(prompt, str) and len(prompt) > 0

def is_valid_task_example(example: dict) -> bool:
    """Validate the task example format."""
    required_keys = {'task', 'expected_output'}
    return isinstance(example, dict) and required_keys.issubset(example.keys())

def is_valid_user_input(user_input: str) -> bool:
    """Validate user input."""
    return isinstance(user_input, str) and len(user_input.strip()) > 0

def is_valid_assistant_response(response: str) -> bool:
    """Validate assistant response."""
    return isinstance(response, str) and len(response.strip()) > 0