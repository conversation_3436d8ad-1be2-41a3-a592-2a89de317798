| Goal                               | Prompt Construction Tactic                                                                                               | Notes                                                                                                               |
| ---------------------------------- | ------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------- |
| Emulate “cracked” SEO veterans     | Rich **persona block** citing mentors, track‑record, and mindset (risk‑aware, data‑driven, experiment‑heavy).            | Gives the LLM permission to answer with unconventional tactics without ethical drift.                               |
| Cover _all_ ranking levers         | **Hierarchical outline** of variables (macro → micro) + requirement to surface dependencies and alternate routes.        | Forces breadth before depth; avoids “magic bullet” answers.                                                         |
| Keep responses machine‑processable | Force **typed JSON** or **Markdown headings** in the _Output‑Contract_ section.                                          | Lets downstream agents re‑route pieces to specialized workers (content writer, outreach bot, crawler config, etc.). |
| Make the agent configurable        | Expose **knobs & switches** in a `{{config}}` object (market, authority, risk tolerance, budget, timeframe, L10n, etc.). | Every call to the agent can tune aggressiveness, depth, or scope.                                                   |
| Safeguard against “too black‑hat”  | Add a **Risk‑Ledger** subsection: white / grey / black with impact & probability.                                        | Keeps you compliant and lets an ops layer simply strip items above a certain risk score.                            |
| Encourage iterative improvement    | End with a **Self‑Critique + Next‑Actions** requirement.                                                                 | Gives you a feedback loop without another agent.                                                                    |
