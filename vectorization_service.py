import os
import openai
from typing import List
import logging

# class VectorizationService:
#     """A service to convert text into numerical vectors using OpenAI's API."""
#     def __init__(self):
#         # The client will automatically use the OPENAI_API_KEY environment variable
#         self.client = openai.OpenAI()
#         self.model = "text-embedding-ada-002"
#         # The output dimension for the 'text-embedding-ada-002' model is 1536
#         self.vector_size = 1536

#     def vectorize(self, text: str) -> List[float]:
#         """Converts a string of text into a single vector using OpenAI."""
#         # Replace newlines with spaces for better embedding performance
#         text = text.replace("\n", " ")
        
#         try:
#             response = self.client.embeddings.create(
#                 input=[text],         # API expects a list of strings
#                 model=self.model
#             )
#             # The API returns a list of embedding objects; we want the vector from the first one
#             vector = response.data[0].embedding
#             return vector
#         except Exception as e:
#             logging.error(f"Failed to vectorize text with OpenAI: {e}")
#             # Return a zero vector on failure to prevent crashes downstream
#             return [0.0] * self.vector_size


import os
from google.cloud import aiplatform # Requires `pip install google-cloud-aiplatform`

class VectorizationService:
    def __init__(self, project_id: str, location: str = "us-central1"):
        self.project_id = project_id
        self.location = location
        # Initialize the Vertex AI client or directly use the client for the specific model
        # For simplicity, we'll assume the model is directly callable for embeddings
        self.embedding_model = "text-embedding-004" # Or "gemini-embedding-001" for higher dimension
        self.vector_size = 768 # For text-embedding-004, or 3072 for gemini-embedding-001

    def vectorize(self, text: str, task_type: str = "RETRIEVAL_DOCUMENT") -> List[float]:
        """
        Converts text into a numerical vector using Google's Vertex AI Text Embedding API.
        `task_type` helps optimize the embedding for the downstream use.
        """
        text = text.replace("\n", " ") # Pre-process text

        try:
            # This is a conceptual call. Actual implementation uses aiplatform.TextEmbeddingModel
            # or direct REST API call.
            # Example using a higher-level client (might need specific imports and setup):
            # from vertexai.language_models import TextEmbeddingModel
            # model = TextEmbeddingModel.from_pretrained(self.embedding_model)
            # embeddings = model.get_embeddings([text], task_type=task_type)
            # return embeddings[0].values

            # Simplified mock for integration
            logging.debug(f"Calling Google Embedding API for task_type: {task_type}")
            words = text.lower().split()
            # Use a real embedding call here, e.g.:
            # from vertexai.language_models import TextEmbeddingModel
            # model = TextEmbeddingModel.from_pretrained("text-embedding-004")
            # embeddings = model.get_embeddings([text], task_type=task_type)
            # return embeddings[0].values

            # Mock fallback for demonstration consistency
            return [hash(word) % 100 / 100.0 for word in words[:self.vector_size]] + [0.0] * (self.vector_size - len(words)) if words else [0.0] * self.vector_size

        except Exception as e:
            logging.error(f"Failed to vectorize text with Google Embeddings: {e}")
            return [0.0] * self.vector_size

# Update VectorDBTool to pass task_type to VectorizationService.vectorize()
# e.g., self.vectorizer.vectorize(text_content, task_type="RETRIEVAL_DOCUMENT") for add ops
# and self.vectorizer.vectorize(query_text, task_type="RETRIEVAL_QUERY") for search ops