"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/on_page/lighthouse/task_post
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'url': 'https://dataforseo.com',
        'for_mobile': False,
        'categories': {
            0: 'seo',
            1: 'performance',
            2: 'pwa'
        },
        'audits': {
            0: 'is-on-https'
        }
    })
try:
    response = client.post('/v3/on_page/lighthouse/task_post', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
