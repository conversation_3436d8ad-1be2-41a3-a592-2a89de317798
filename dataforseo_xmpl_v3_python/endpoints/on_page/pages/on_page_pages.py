"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/on_page/pages
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'id': '08101056-0696-0216-0000-721423f84e3d',
        'filters': {
            0: {
                0: 'resource_type',
                1: '=',
                2: 'html'
            },
            1: 'and',
            2: {
                0: 'meta.description',
                1: 'like',
                2: '%OnPage%'
            }
        },
        'limit': 3
    })
try:
    response = client.post('/v3/on_page/pages', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
