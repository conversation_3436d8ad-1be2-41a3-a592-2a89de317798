"""
Method: GET
Endpoint: https://api.dataforseo.com/v3/business_data/google/my_business_info/task_get/$id
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

try:
    task_id = '09171517-0696-0242-0000-a96bc1ad0bce'
    response = client.get(f'/v3/business_data/google/my_business_info/task_get/{task_id}')
    print(response)
    # do something with get result
except Exception as e:
    print(f'An error occurred: {e}')
