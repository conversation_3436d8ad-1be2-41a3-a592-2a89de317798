"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/dataforseo_labs/apple/app_competitors/live
@see https://docs.dataforseo.com/v3/dataforseo_labs/apple/app_competitors/live/
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'app_id': '310633997',
        'language_name': 'English',
        'location_code': 2840,
        'filters': {
            0: 'intersections',
            1: '>',
            2: 500
        },
        'order_by': {
            0: 'intersections,desc'
        },
        'limit': 10
    })
try:
    response = client.post('/v3/dataforseo_labs/apple/app_competitors/live', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
