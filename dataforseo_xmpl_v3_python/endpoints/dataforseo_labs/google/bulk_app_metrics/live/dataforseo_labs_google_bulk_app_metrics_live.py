"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/dataforseo_labs/google/bulk_app_metrics/live
@see https://docs.dataforseo.com/v3/dataforseo_labs/google/bulk_app_metrics/live/
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'app_ids': {
            0: 'org.telegram.messenger',
            1: 'com.zhiliaoapp.musically',
            2: 'com.whatsapp',
            3: 'com.facebook.katana'
        },
        'language_name': 'English',
        'location_code': 2840
    })
try:
    response = client.post('/v3/dataforseo_labs/google/bulk_app_metrics/live', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
