"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/dataforseo_labs/google/search_intent/live
@see https://docs.dataforseo.com/v3/dataforseo_labs/google/search_intent/live/
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'language_name': 'English',
        'keywords': {
            0: 'login page',
            1: 'transmitter',
            2: 'al<PERSON> <PERSON><PERSON>',
            3: 'pizza new york'
        }
    })
try:
    response = client.post('/v3/dataforseo_labs/google/search_intent/live', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
