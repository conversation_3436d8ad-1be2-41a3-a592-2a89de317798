"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/app_data/apple/app_listings/search/live
@see https://docs.dataforseo.com/v3/app_data/apple/app_listings/search/live
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'title': 'vpn',
        'order_by': {
            0: 'item.rating.value,desc'
        },
        'filters': {
            0: {
                0: 'item.rating.value',
                1: '>',
                2: 4.5
            }
        },
        'internal_list_limit': 2,
        'limit': 1
    })
try:
    response = client.post('/v3/app_data/apple/app_listings/search/live', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
