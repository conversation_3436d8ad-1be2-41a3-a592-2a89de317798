"""
Method: GET
Endpoint: https://api.dataforseo.com/v3/app_data/google/app_list/task_get/html/$id
@see https://docs.dataforseo.com/v3/app_data/google/app_list/task_get/html/
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

try:
    task_id = '04201918-2692-0422-0000-11e9083e48c7'
    response = client.get(f'/v3/app_data/google/app_list/task_get/html/{task_id}')
    print(response)
    # do something with get result
except Exception as e:
    print(f'An error occurred: {e}')
