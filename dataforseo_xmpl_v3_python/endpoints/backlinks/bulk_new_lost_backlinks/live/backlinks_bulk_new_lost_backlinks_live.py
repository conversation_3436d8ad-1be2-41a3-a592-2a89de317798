"""
Method: POST
Endpoint: https://api.dataforseo.com/v3/backlinks/bulk_new_lost_backlinks/live
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

post_data = []
post_data.append({
        'targets': {
            0: 'forbes.com',
            1: 'cnn.com',
            2: 'bbc.com',
            3: 'yelp.com',
            4: 'https://www.apple.com/iphone/',
            5: 'https://ahrefs.com/blog/',
            6: 'ibm.com',
            7: 'https://variety.com/',
            8: 'https://stackoverflow.com/',
            9: 'www.trustpilot.com'
        },
        'date_from': '2021-09-01'
    })
try:
    response = client.post('/v3/backlinks/bulk_new_lost_backlinks/live', post_data)
    print(response)
    # do something with post result
except Exception as e:
    print(f'An error occurred: {e}')
