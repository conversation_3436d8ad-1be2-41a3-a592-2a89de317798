"""
Method: GET
Endpoint: https://api.dataforseo.com/v3/merchant/amazon/reviews/task_get/html/$id
@see https://docs.dataforseo.com/v3/merchant/amazon/reviews/task_get/html/?bash
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

try:
    task_id = '03271112-0696-0415-0000-5d6c3976b68a'
    response = client.get(f'/v3/merchant/amazon/reviews/task_get/html/{task_id}')
    print(response)
    # do something with get result
except Exception as e:
    print(f'An error occurred: {e}')
