"""
Method: GET
Endpoint: https://api.dataforseo.com/v3/keywords_data/bing/keyword_suggestions_for_url/task_get/$id
@see https://docs.dataforseo.com/v3/keywords_data/bing/keyword_suggestions_for_url/task_get
"""

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../')))
from lib.client import RestClient
from lib.config import username, password
client = RestClient(username, password)

try:
    task_id = '10071309-0696-0597-0000-3ff23305b5ca'
    response = client.get(f'/v3/keywords_data/bing/keyword_suggestions_for_url/task_get/{task_id}')
    print(response)
    # do something with get result
except Exception as e:
    print(f'An error occurred: {e}')
